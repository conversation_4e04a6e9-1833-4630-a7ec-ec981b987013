<template>
  <div class="excel-editor-container">
    <div class="header-actions">
      <el-button
        icon="ArrowLeft"
        @click="handleBack"
        v-if="!isFullScreen"
      >
        返回列表
      </el-button>
      <h2>{{ excelFile.fileName }}</h2>
      <div class="action-buttons">
        <!-- 管理员权限按钮 -->
        <template v-if="isAdmin">
          <el-button
            type="primary"
            icon="Setting"
            @click="showPermissionDialog = true"
          >
            配置列权限
          </el-button>
        </template>

        <!-- 通用按钮 -->
        <el-button
          type="success"
          icon="Check"
          @click="handleSave"
          :loading="saveLoading"
        >
          保存修改
        </el-button>
        <el-button
          icon="Refresh"
          @click="handleRefresh"
        >
          刷新数据
        </el-button>
        <el-button
          icon="Maximize"
          @click="toggleFullScreen"
        >
          {{ isFullScreen ? '退出全屏' : '全屏编辑' }}
        </el-button>
      </div>
    </div>

    <!-- 表格工具栏 -->
    <div class="table-toolbar">
      <el-button
        type="primary"
        icon="Plus"
        size="small"
        @click="addNewRow"
        class="add-row-btn"
      >
        添加行
      </el-button>
      <el-input
        v-model="searchText"
        placeholder="搜索内容..."
        prefix-icon="Search"
        size="small"
        class="search-input"
      ></el-input>
      <div class="table-info">
        <span>共 {{ tableData.length }} 行数据</span>
        <span v-if="isAdmin">| 可编辑列: {{ editableColumns.length }} 列</span>
      </div>
    </div>

    <!-- Excel表格 -->
    <div class="table-container">
      <el-table
        :data="tableData"
        border
        stripe
        :size="isMobile ? 'default' : 'small'"
        :cell-style="cellStyle"
        :highlight-current-row="true"
        @cell-click="handleCellClick"
        class="responsive-table"
        :class="{ 'mobile-table': isMobile }"
      >
        <el-table-column
          type="index"
          label="行号"
          :width="isMobile ? 50 : 60"
          :fixed="isMobile ? false : 'left'"
        ></el-table-column>
        <el-table-column
          v-for="column in visibleColumns"
          :key="column.prop"
          :prop="column.prop"
          :label="column.label"
          :width="getColumnWidth(column)"
          :min-width="getColumnMinWidth(column)"
          :fixed="getColumnFixed(column)"
          :show-overflow-tooltip="true"
        >
          <template #default="{ row, column: col }">
            <template v-if="isEditable(col.property, row)">
              <el-input
                v-model="row[col.property]"
                :size="isMobile ? 'default' : 'small'"
                :disabled="!isEditable(col.property, row)"
                @change="handleCellChange(row, col.property)"
                class="editable-cell"
                :class="{ 'mobile-input': isMobile }"
              ></el-input>
            </template>
            <template v-else>
              <div class="cell-content" :class="{ 'mobile-cell': isMobile }">
                {{ row[col.property] }}
              </div>
            </template>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          :width="isMobile ? 80 : 120"
          :fixed="isMobile ? false : 'right'"
        >
          <template #default="{ row }">
            <el-button
              type="danger"
              :icon="isMobile ? 'Delete' : undefined"
              :size="isMobile ? 'small' : 'small'"
              @click="deleteRow(row)"
              v-if="canDeleteRow(row)"
              :class="{ 'mobile-delete-btn': isMobile }"
            >
              <span v-if="!isMobile">删除</span>
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 移动端列选择器 -->
    <div v-if="isMobile" class="mobile-column-selector">
      <el-select
        v-model="selectedMobileColumns"
        multiple
        placeholder="选择要显示的列"
        size="small"
        @change="updateVisibleColumns"
        class="column-selector"
      >
        <el-option
          v-for="column in columns"
          :key="column.prop"
          :label="column.label"
          :value="column.prop"
        ></el-option>
      </el-select>
      <el-button
        size="small"
        @click="showAllColumns"
        class="show-all-btn"
      >
        显示全部
      </el-button>
    </div>

    <!-- 权限配置对话框 -->
    <el-dialog
      v-model="showPermissionDialog"
      title="配置列编辑权限"
      width="600px"
    >
      <el-table :data="columns" border stripe size="small">
        <el-table-column prop="label" label="列名" width="180"></el-table-column>
        <el-table-column prop="prop" label="字段名" width="180"></el-table-column>
        <el-table-column label="可编辑">
          <template #default="{ row }">
            <el-switch
              v-model="row.editable"
              active-color="#1890ff"
              inactive-text="不可编辑"
              active-text="可编辑"
              @change="updateEditableColumns"
            ></el-switch>
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <el-button @click="showPermissionDialog = false">取消</el-button>
        <el-button type="primary" @click="savePermissionSettings">保存设置</el-button>
      </template>
    </el-dialog>

    <!-- 全屏遮罩 -->
    <div
      v-if="isFullScreen"
      class="fullscreen-mask"
      @click="toggleFullScreen"
    ></div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useUserStore } from '../stores/user';
import { ElMessage, ElMessageBox } from 'element-plus';

const route = useRoute();
const router = useRouter();
const userStore = useUserStore();

// 状态变量
const saveLoading = ref(false);
const showPermissionDialog = ref(false);
const searchText = ref('');
const isFullScreen = ref(false);
const excelFileId = ref(route.params.id);
const originalData = ref([]);
const isMobile = ref(false);
const selectedMobileColumns = ref([]);
const visibleColumns = ref([]);

// Excel文件信息（模拟）
const excelFile = ref({
  id: excelFileId.value,
  fileName: '销售数据报表.xlsx',
  description: '2023年度销售数据汇总表'
});

// 列定义（模拟，包含权限配置）
const columns = ref([
  { prop: 'productName', label: '产品名称', width: 180, editable: true },
  { prop: 'category', label: '产品类别', width: 120, editable: true },
  { prop: 'salesVolume', label: '销量', width: 100, editable: true },
  { prop: 'unitPrice', label: '单价', width: 100, editable: true },
  { prop: 'totalSales', label: '销售额', width: 120, editable: false },
  { prop: 'region', label: '销售区域', width: 120, editable: true },
  { prop: 'salesPerson', label: '销售人员', width: 120, editable: true },
  { prop: 'saleDate', label: '销售日期', width: 140, editable: true },
  { prop: 'notes', label: '备注', width: 200, editable: true }
]);

// 表格数据（模拟）
const tableData = ref([]);

// 计算属性
const isAdmin = computed(() => userStore.currentUser?.role === 'ADMIN');
const editableColumns = computed(() => {
  return columns.value.filter(col => col.editable).map(col => col.prop);
});

// 单元格样式（控制可编辑状态的显示）
const cellStyle = ({ row, column }) => {
  if (!isEditable(column.property, row)) {
    return { 'background-color': '#f5f5f5', 'color': '#606266' };
  }
  return {};
};

// 初始化表格数据
const initTableData = () => {
  // 模拟数据
  const mockData = [
    { id: 1, productName: '笔记本电脑', category: '电子产品', salesVolume: 120, unitPrice: 5999, totalSales: 719880, region: '华东', salesPerson: '张三', saleDate: '2023-01-15', notes: '企业采购' },
    { id: 2, productName: '智能手机', category: '电子产品', salesVolume: 350, unitPrice: 3999, totalSales: 1399650, region: '全国', salesPerson: '李四', saleDate: '2023-01-20', notes: '线上促销' },
    { id: 3, productName: '平板电脑', category: '电子产品', salesVolume: 85, unitPrice: 2999, totalSales: 254915, region: '华南', salesPerson: '王五', saleDate: '2023-02-05', notes: '' },
    { id: 4, productName: '无线耳机', category: '配件', salesVolume: 230, unitPrice: 899, totalSales: 206770, region: '华北', salesPerson: '赵六', saleDate: '2023-02-18', notes: '新品推广' },
    { id: 5, productName: '智能手表', category: '穿戴设备', salesVolume: 150, unitPrice: 1599, totalSales: 239850, region: '华东', salesPerson: '张三', saleDate: '2023-03-10', notes: '节日促销' }
  ];

  tableData.value = mockData;
  // 保存原始数据用于比较修改
  originalData.value = JSON.parse(JSON.stringify(mockData));
};

// 判断单元格是否可编辑
const isEditable = (columnProp, row) => {
  // 管理员可以编辑所有单元格
  if (isAdmin.value) return true;
  // 普通用户只能编辑特定列
  return columns.value.find(col => col.prop === columnProp)?.editable;
};

// 判断行是否可删除
const canDeleteRow = (row) => {
  // 管理员可以删除所有行
  if (isAdmin.value) return true;
  // 普通用户只能删除自己添加的行
  return row.isNew || (row.createdBy && row.createdBy === userStore.currentUser.username);
};

// 处理单元格点击事件
const handleCellClick = (row, column, cell, event) => {
  // 非编辑状态的单元格点击不触发任何操作
  if (!isEditable(column.property, row)) {
    event.stopPropagation();
    return;
  }
};

// 处理单元格内容变化
const handleCellChange = (row, column) => {
  // 自动计算销售额
  if (column === 'salesVolume' || column === 'unitPrice') {
    row.totalSales = row.salesVolume * row.unitPrice;
  }
};

// 添加新行
const addNewRow = () => {
  const newRow = {
    id: Date.now(),
    productName: '',
    category: '',
    salesVolume: 0,
    unitPrice: 0,
    totalSales: 0,
    region: '',
    salesPerson: isAdmin.value ? '' : userStore.currentUser.username,
    saleDate: new Date().toISOString().split('T')[0],
    notes: '',
    isNew: true,
    createdBy: userStore.currentUser.username
  };

  tableData.value.unshift(newRow);
  // 滚动到新行
  nextTick(() => {
    // 这里可以添加滚动到新行的逻辑
  });
};

// 删除行
const deleteRow = (row) => {
  ElMessageBox.confirm(`确定要删除第 ${tableData.value.indexOf(row) + 1} 行吗？`, '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const index = tableData.value.indexOf(row);
    if (index !== -1) {
      tableData.value.splice(index, 1);
      ElMessage.success('行删除成功');
    }
  }).catch(() => {
    // 取消删除
  });
};

// 保存修改
const handleSave = async () => {
  saveLoading.value = true;
  try {
    // 模拟API请求延迟
    await new Promise(resolve => setTimeout(resolve, 800));

    // 检查是否有修改
    const hasChanges = JSON.stringify(tableData.value) !== JSON.stringify(originalData.value);
    if (!hasChanges) {
      ElMessage.info('没有检测到任何修改');
      return;
    }

    // 模拟保存成功
    ElMessage.success('数据保存成功');
    // 更新原始数据
    originalData.value = JSON.parse(JSON.stringify(tableData.value));
    // 清除新行标记
    tableData.value.forEach(row => delete row.isNew);
  } catch (error) {
    ElMessage.error('保存失败，请重试');
  } finally {
    saveLoading.value = false;
  }
};

// 刷新数据
const handleRefresh = () => {
  ElMessageBox.confirm('确定要刷新数据吗？未保存的修改将会丢失。', '确认刷新', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'info'
  }).then(() => {
    initTableData();
    ElMessage.success('数据已刷新');
  }).catch(() => {
    // 取消刷新
  });
};

// 返回列表
const handleBack = () => {
  router.push('/excel');
};

// 切换全屏模式
const toggleFullScreen = () => {
  isFullScreen.value = !isFullScreen.value;
  document.body.classList.toggle('excel-fullscreen');
};

// 响应式相关方法
const handleResize = () => {
  isMobile.value = window.innerWidth < 768;
  updateVisibleColumns();
};

// 获取列宽度
const getColumnWidth = (column) => {
  if (isMobile.value) {
    return undefined; // 移动端不设置固定宽度
  }
  return column.width || 120;
};

// 获取列最小宽度
const getColumnMinWidth = (column) => {
  if (isMobile.value) {
    return 80; // 移动端最小宽度
  }
  return column.width || 120;
};

// 获取列固定设置
const getColumnFixed = (column) => {
  if (isMobile.value) {
    return false; // 移动端不固定列
  }
  return column.fixed;
};

// 更新可见列
const updateVisibleColumns = () => {
  if (isMobile.value) {
    if (selectedMobileColumns.value.length === 0) {
      // 默认显示前3列
      visibleColumns.value = columns.value.slice(0, 3);
      selectedMobileColumns.value = visibleColumns.value.map(col => col.prop);
    } else {
      visibleColumns.value = columns.value.filter(col =>
        selectedMobileColumns.value.includes(col.prop)
      );
    }
  } else {
    visibleColumns.value = columns.value;
  }
};

// 显示所有列
const showAllColumns = () => {
  selectedMobileColumns.value = columns.value.map(col => col.prop);
  updateVisibleColumns();
};

// 更新可编辑列
const updateEditableColumns = () => {
  // 这里只是临时更新，需要点击保存按钮才会永久保存
};

// 保存列权限设置
const savePermissionSettings = () => {
  // 模拟保存权限设置
  ElMessage.success('列权限设置已保存');
  showPermissionDialog.value = false;
};

// 页面加载时初始化
onMounted(() => {
  initTableData();

  // 初始化响应式
  handleResize();
  window.addEventListener('resize', handleResize);

  // 清理函数
  return () => {
    window.removeEventListener('resize', handleResize);
  };
});
</script>

<style scoped>
.excel-editor-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.header-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e8e8e8;
}

.header-actions h2 {
  margin: 0;
  flex: 1;
  padding: 0 15px;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding: 5px 0;
}

.add-row-btn {
  margin-right: 10px;
}

.search-input {
  width: 250px;
}

.table-info {
  color: #606266;
  font-size: 12px;
  display: flex;
  gap: 15px;
}

.table-container {
  flex: 1;
  overflow: auto;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  min-height: 0; /* 确保flex子元素可以收缩 */
}

.responsive-table {
  width: 100%;
  min-width: 100%;
}

.mobile-table {
  font-size: 12px;
}

.mobile-table .el-table__cell {
  padding: 8px 4px;
}

.editable-cell .el-input {
  width: 100%;
}

.mobile-input .el-input__inner {
  padding: 4px 8px;
  font-size: 12px;
}

.cell-content {
  word-break: break-word;
  line-height: 1.4;
}

.mobile-cell {
  font-size: 12px;
  padding: 2px 0;
}

.mobile-delete-btn {
  padding: 4px 8px;
  min-width: auto;
}

.mobile-column-selector {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 10px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.column-selector {
  flex: 1;
  min-width: 200px;
}

.show-all-btn {
  flex-shrink: 0;
}

.fullscreen-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  font-size: 20px;
  cursor: pointer;
}

/* 响应式样式 */
@media (max-width: 1200px) {
  .header-actions {
    flex-wrap: wrap;
  }

  .header-actions h2 {
    width: 100%;
    margin-bottom: 10px;
  }

  .action-buttons {
    flex-wrap: wrap;
    gap: 8px;
  }
}

@media (max-width: 768px) {
  .excel-editor-container {
    padding: 0;
  }

  .header-actions {
    padding: 10px;
    flex-direction: column;
    align-items: stretch;
  }

  .header-actions h2 {
    font-size: 18px;
    margin-bottom: 10px;
    text-align: center;
  }

  .action-buttons {
    justify-content: center;
    flex-wrap: wrap;
    gap: 8px;
  }

  .action-buttons .el-button {
    flex: 1;
    min-width: 80px;
    max-width: 120px;
  }

  .table-toolbar {
    flex-direction: column;
    gap: 10px;
    padding: 10px;
  }

  .search-input {
    width: 100%;
  }

  .table-info {
    width: 100%;
    text-align: center;
    font-size: 12px;
  }

  .table-container {
    margin: 0 10px;
    border-radius: 0;
  }

  /* 表格在移动端的特殊样式 */
  .responsive-table .el-table__header-wrapper {
    overflow-x: auto;
  }

  .responsive-table .el-table__body-wrapper {
    overflow-x: auto;
  }

  /* 权限配置对话框在移动端的调整 */
  .el-dialog {
    width: 95% !important;
    margin: 0 auto;
  }
}

@media (max-width: 480px) {
  .header-actions {
    padding: 8px;
  }

  .header-actions h2 {
    font-size: 16px;
  }

  .action-buttons .el-button {
    font-size: 12px;
    padding: 6px 8px;
  }

  .table-toolbar {
    padding: 8px;
  }

  .table-container {
    margin: 0 8px;
  }

  .mobile-column-selector {
    margin: 8px;
    padding: 8px;
  }

  .column-selector {
    min-width: 150px;
  }
}

/* 横屏模式优化 */
@media (max-width: 768px) and (orientation: landscape) {
  .header-actions {
    flex-direction: row;
    align-items: center;
  }

  .header-actions h2 {
    flex: 1;
    text-align: left;
    margin-bottom: 0;
  }

  .action-buttons {
    flex-shrink: 0;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .el-table .el-table__cell {
    padding: 12px 8px;
  }

  .el-button {
    min-height: 44px;
    min-width: 44px;
  }

  .editable-cell .el-input__inner {
    min-height: 40px;
  }
}
</style>