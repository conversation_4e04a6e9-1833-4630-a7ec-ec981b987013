<template>
  <div class="user-management-container">
    <div class="header-actions">
      <h2>用户管理</h2>
      <el-button
        type="primary"
        icon="Plus"
        @click="openAddUserDialog"
      >
        添加用户
      </el-button>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-filter">
      <el-input
        v-model="searchForm.username"
        placeholder="搜索用户名"
        prefix-icon="Search"
        size="small"
        class="search-input"
      ></el-input>
      <el-select
        v-model="searchForm.role"
        placeholder="选择角色"
        size="small"
        class="role-select"
      >
        <el-option label="所有角色" value=""></el-option>
        <el-option label="管理员" value="ADMIN"></el-option>
        <el-option label="普通用户" value="USER"></el-option>
      </el-select>
      <el-button
        icon="Refresh"
        size="small"
        @click="resetSearch"
      >
        重置
      </el-button>
    </div>

    <!-- 用户表格 -->
    <el-table
      :data="filteredUsers"
      border
      stripe
      size="small"
      :header-cell-style="{ 'background-color': '#f5f7fa' }"
    >
      <el-table-column type="index" label="序号" width="60"></el-table-column>
      <el-table-column prop="username" label="用户名" width="140"></el-table-column>
      <el-table-column prop="email" label="邮箱" width="200"></el-table-column>
      <el-table-column prop="role" label="角色" width="100">
        <template #default="{ row }">
          <el-tag :type="row.role === 'ADMIN' ? 'primary' : 'success'">
            {{ row.role === 'ADMIN' ? '管理员' : '普通用户' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-switch
            v-model="row.status"
            active-value="ACTIVE"
            inactive-value="INACTIVE"
            @change="handleStatusChange(row)"
            :disabled="!canModifyUser(row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column prop="createdAt" label="创建时间" width="180"></el-table-column>
      <el-table-column label="操作" width="180" fixed="right">
        <template #default="{ row }">
          <el-button
            type="primary"
            icon="Edit"
            size="small"
            @click="openEditUserDialog(row)"
            :disabled="!canModifyUser(row)"
          ></el-button>
          <el-button
            type="danger"
            icon="Delete"
            size="small"
            @click="confirmDeleteUser(row)"
            :disabled="!canDeleteUser(row)"
          ></el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:currentPage="pagination.currentPage"
        v-model:pageSize="pagination.pageSize"
        :page-sizes="[10, 20, 50]"
        :total="filteredUsers.length"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>

    <!-- 添加/编辑用户对话框 -->
    <el-dialog
      v-model="userDialogVisible"
      :title="isEditMode ? '编辑用户' : '添加用户'"
      width="500px"
    >
      <el-form
        ref="userFormRef"
        :model="userForm"
        :rules="userFormRules"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="userForm.username" :disabled="isEditMode"></el-input>
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email" type="email"></el-input>
        </el-form-item>
        <el-form-item label="密码" prop="password" v-if="!isEditMode">
          <el-input v-model="userForm.password" type="password"></el-input>
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword" v-if="!isEditMode">
          <el-input v-model="userForm.confirmPassword" type="password"></el-input>
        </el-form-item>
        <el-form-item label="角色" prop="role">
          <el-select v-model="userForm.role">
            <el-option label="管理员" value="ADMIN"></el-option>
            <el-option label="普通用户" value="USER"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="userForm.status">
            <el-option label="启用" value="ACTIVE"></el-option>
            <el-option label="禁用" value="INACTIVE"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="userDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitUserForm">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue';
import { useUserStore } from '../stores/user';
import { ElMessage, ElMessageBox } from 'element-plus';

// 状态管理
const userStore = useUserStore();

// 表格数据
const users = ref([]);
const searchForm = ref({
  username: '',
  role: ''
});

// 分页
const pagination = ref({
  currentPage: 1,
  pageSize: 10
});

// 用户对话框
const userDialogVisible = ref(false);
const isEditMode = ref(false);
const userFormRef = ref(null);
const userForm = ref({
  username: '',
  email: '',
  password: '',
  confirmPassword: '',
  role: 'USER',
  status: 'ACTIVE'
});

// 表单验证规则
const userFormRules = ref({
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 30, message: '密码长度在 6 到 30 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== userForm.value.password) {
          callback(new Error('两次输入密码不一致'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
});

// 筛选用户数据
const filteredUsers = computed(() => {
  return users.value.filter(user => {
    const matchesUsername = user.username.toLowerCase().includes(searchForm.value.username.toLowerCase());
    const matchesRole = !searchForm.value.role || user.role === searchForm.value.role;
    return matchesUsername && matchesRole;
  });
});

// 初始化用户数据
const initUserData = () => {
  // 模拟用户数据
  users.value = [
    {
      id: 1,
      username: 'admin',
      email: '<EMAIL>',
      role: 'ADMIN',
      status: 'ACTIVE',
      createdAt: '2023-01-15 09:30:00'
    },
    {
      id: 2,
      username: 'user1',
      email: '<EMAIL>',
      role: 'USER',
      status: 'ACTIVE',
      createdAt: '2023-01-20 14:20:00'
    },
    {
      id: 3,
      username: 'user2',
      email: '<EMAIL>',
      role: 'USER',
      status: 'INACTIVE',
      createdAt: '2023-02-05 10:15:00'
    },
    {
      id: 4,
      username: 'user3',
      email: '<EMAIL>',
      role: 'USER',
      status: 'ACTIVE',
      createdAt: '2023-02-18 16:40:00'
    },
    {
      id: 5,
      username: 'user4',
      email: '<EMAIL>',
      role: 'USER',
      status: 'ACTIVE',
      createdAt: '2023-03-10 11:25:00'
    }
  ];
};

// 检查是否可以修改用户
const canModifyUser = (user) => {
  // 不能修改自己
  if (user.username === userStore.currentUser.username) return false;
  // 只能修改普通用户
  return user.role === 'USER';
};

// 检查是否可以删除用户
const canDeleteUser = (user) => {
  // 不能删除自己
  if (user.username === userStore.currentUser.username) return false;
  // 不能删除管理员
  return user.role === 'USER';
};

// 打开添加用户对话框
const openAddUserDialog = () => {
  isEditMode.value = false;
  userForm.value = {
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    role: 'USER',
    status: 'ACTIVE'
  };
  userDialogVisible.value = true;
  nextTick(() => {
    userFormRef.value?.resetFields();
  });
};

// 打开编辑用户对话框
const openEditUserDialog = (user) => {
  isEditMode.value = true;
  userForm.value = {
    username: user.username,
    email: user.email,
    role: user.role,
    status: user.status
  };
  userDialogVisible.value = true;
  nextTick(() => {
    userFormRef.value?.resetFields();
  });
};

// 提交用户表单
const submitUserForm = async () => {
  await userFormRef.value.validate();

  if (isEditMode.value) {
    // 编辑用户
    const index = users.value.findIndex(u => u.username === userForm.value.username);
    if (index !== -1) {
      users.value[index] = {
        ...users.value[index],
        email: userForm.value.email,
        role: userForm.value.role,
        status: userForm.value.status
      };
      ElMessage.success('用户编辑成功');
    }
  } else {
    // 添加用户
    const newUser = {
      id: Date.now(),
      username: userForm.value.username,
      email: userForm.value.email,
      role: userForm.value.role,
      status: userForm.value.status,
      createdAt: new Date().toLocaleString()
    };
    users.value.unshift(newUser);
    ElMessage.success('用户添加成功');
  }

  userDialogVisible.value = false;
};

// 处理状态变更
const handleStatusChange = (user) => {
  const newStatus = user.status === 'ACTIVE' ? 'INACTIVE' : 'ACTIVE';
  ElMessageBox.confirm(`确定要${newStatus === 'ACTIVE' ? '启用' : '禁用'}用户 ${user.username} 吗？`, '确认操作', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    user.status = newStatus;
    ElMessage.success(`用户已${newStatus === 'ACTIVE' ? '启用' : '禁用'}`);
  }).catch(() => {
    // 恢复原始状态
    user.status = user.status === 'ACTIVE' ? 'ACTIVE' : 'INACTIVE';
  });
};

// 确认删除用户
const confirmDeleteUser = (user) => {
  ElMessageBox.confirm(`确定要删除用户 ${user.username} 吗？此操作不可恢复。`, '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'danger'
  }).then(() => {
    const index = users.value.findIndex(u => u.id === user.id);
    if (index !== -1) {
      users.value.splice(index, 1);
      ElMessage.success('用户删除成功');
    }
  }).catch(() => {
    // 取消删除
  });
};

// 重置搜索
const resetSearch = () => {
  searchForm.value = {
    username: '',
    role: ''
  };
  pagination.value.currentPage = 1;
};

// 分页大小变更
const handleSizeChange = (val) => {
  pagination.value.pageSize = val;
};

// 当前页变更
const handleCurrentChange = (val) => {
  pagination.value.currentPage = val;
};

// 页面加载时初始化
onMounted(() => {
  initUserData();
});
</script>

<style scoped>
.user-management-container {
  padding: 20px;
}

.header-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-filter {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
  align-items: center;
}

.search-input {
  width: 250px;
}

.role-select {
  width: 150px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .search-filter {
    flex-wrap: wrap;
  }

  .search-input,
  .role-select {
    width: 100%;
  }
}
</style>