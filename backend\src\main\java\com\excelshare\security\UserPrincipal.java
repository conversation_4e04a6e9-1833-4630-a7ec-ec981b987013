package com.excelshare.security;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户主体类
 * 用于在Security Context中存储用户信息
 * 
 * <AUTHOR> Team
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserPrincipal {
    
    private Long userId;
    private String username;
    private String role;
    
    /**
     * 检查是否为管理员
     */
    public boolean isAdmin() {
        return "ADMIN".equals(role);
    }
    
    /**
     * 检查是否为普通用户
     */
    public boolean isUser() {
        return "USER".equals(role);
    }
}
