import { createRouter, createWebHistory } from 'vue-router';
import Login from '../views/Login.vue';
import Register from '../views/Register.vue';
import AdminDashboard from '../views/AdminDashboard.vue';
import UserManagement from '../views/UserManagement.vue';
import ExcelList from '../views/ExcelList.vue';
import ExcelEditor from '../views/ExcelEditor.vue';
import NotFound from '../views/NotFound.vue';
import Layout from '../components/Layout.vue';
import { useUserStore } from '../stores/user';

// 路由守卫函数
const requireAuth = (to, from, next) => {
  const userStore = useUserStore();
  if (!userStore.isLoggedIn) {
    next('/login');
  } else if (to.meta.requiresAdmin && (!userStore.currentUser || userStore.currentUser.role !== 'ADMIN')) {
    next('/excel');
  } else {
    next();
  }
};

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { guest: true }
  },
  {
    path: '/register',
    name: 'Register',
    component: Register,
    meta: { guest: true }
  },
  {
    path: '/',
    component: Layout,
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        redirect: '/excel'
      },
      {
        path: '/admin/dashboard',
        name: 'AdminDashboard',
        component: AdminDashboard,
        meta: { requiresAdmin: true }
      },
      {
        path: '/admin/users',
        name: 'UserManagement',
        component: UserManagement,
        meta: { requiresAdmin: true }
      },
      {
        path: '/excel',
        name: 'ExcelList',
        component: ExcelList
      },
      {
        path: '/excel/:id',
        name: 'ExcelEditor',
        component: ExcelEditor
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: NotFound
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes
});

// 全局路由守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore();
  const isLoggedIn = userStore.isLoggedIn;

  // 如果需要登录但未登录，则重定向到登录页
  if (to.meta.requiresAuth && !isLoggedIn) {
    next('/login');
  }
  // 如果已登录但访问登录/注册页，则重定向到对应主页
  else if (isLoggedIn && to.meta.guest) {
    next(userStore.currentUser.role === 'ADMIN' ? '/admin/dashboard' : '/excel');
  }
  // 如果需要管理员权限但不是管理员，则重定向到用户主页
  else if (to.meta.requiresAdmin && (!userStore.currentUser || userStore.currentUser.role !== 'ADMIN')) {
    next('/excel');
  }
  else {
    next();
  }
});

export default router;