package com.excelshare.dto;

import lombok.Data;
import javax.validation.constraints.Email;
import javax.validation.constraints.Size;

/**
 * 更新用户请求DTO
 * 
 * <AUTHOR> Team
 */
@Data
public class UserUpdateRequest {
    
    @Size(min = 3, max = 50, message = "用户名长度必须在3-50个字符之间")
    private String username;
    
    @Size(min = 6, message = "密码长度不能少于6个字符")
    private String password;
    
    @Email(message = "邮箱格式不正确")
    private String email;
    
    private String role; // ADMIN 或 USER
}
