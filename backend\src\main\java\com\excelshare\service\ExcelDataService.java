package com.excelshare.service;

import com.excelshare.entity.ExcelData;

import java.util.List;
import java.util.Map;

/**
 * Excel数据服务接口
 * 
 * <AUTHOR> Team
 */
public interface ExcelDataService {

    /**
     * 根据ID查询Excel数据
     */
    ExcelData findById(Long id);

    /**
     * 根据Excel文件ID查询数据
     */
    List<ExcelData> findByExcelId(Long excelId, Long userId, String role);

    /**
     * 保存Excel数据
     */
    ExcelData saveExcelData(Long excelId, Integer rowNumber, Map<String, Object> cellData, Long userId);

    /**
     * 更新Excel数据
     */
    ExcelData update(ExcelData excelData);

    /**
     * 删除Excel数据行
     */
    boolean deleteRow(Long excelId, Integer rowNumber, Long userId, String role);

    /**
     * 添加新行
     */
    ExcelData addNewRow(Long excelId, Map<String, Object> cellData, Long userId);

    /**
     * 根据Excel文件ID删除所有数据
     */
    boolean deleteByExcelId(Long excelId, Long userId, String role);

    /**
     * 批量导入数据
     */
    boolean importData(Long excelId, List<Map<String, Object>> dataList, Long userId);

    /**
     * 统计Excel文件的数据行数
     */
    int countByExcelId(Long excelId);

    /**
     * 获取Excel文件的最大行号
     */
    int getMaxRowNumber(Long excelId);
}
