package com.excelshare.service.impl;

import com.excelshare.entity.ExcelData;
import com.excelshare.mapper.ExcelDataMapper;
import com.excelshare.service.ExcelDataService;
import com.excelshare.service.ExcelFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Excel数据服务实现类 - 数据库版本
 *
 * <AUTHOR> Team
 */
@Service
public class ExcelDataServiceImpl implements ExcelDataService {

    @Autowired
    private ExcelDataMapper excelDataMapper;

    @Autowired
    private ExcelFileService excelFileService;

    @Override
    public ExcelData findById(Long id) {
        return excelDataMapper.findById(id);
    }

    @Override
    public List<ExcelData> findByExcelId(Long excelId, Long userId, String role) {
        // 检查文件访问权限
        if (!excelFileService.hasFileAccess(excelId, userId, role)) {
            throw new RuntimeException("Access denied");
        }

        return excelDataMapper.findByExcelId(excelId);
    }

    @Override
    public ExcelData saveExcelData(Long excelId, Integer rowNumber, Map<String, Object> cellData, Long userId) {
        // 查找是否已存在该行数据
        ExcelData existingData = excelDataMapper.findByExcelIdAndRowNumber(excelId, rowNumber);

        if (existingData != null) {
            // 更新现有数据
            existingData.setCellData(cellData);
            existingData.setUpdatedAt(LocalDateTime.now());
            excelDataMapper.update(existingData);
            return existingData;
        } else {
            // 创建新数据
            ExcelData newData = new ExcelData();
            newData.setExcelId(excelId);
            newData.setUserId(userId);
            newData.setRowNumber(rowNumber);
            newData.setCellData(cellData);
            newData.setCreatedAt(LocalDateTime.now());
            newData.setUpdatedAt(LocalDateTime.now());
            excelDataMapper.insert(newData);
            return newData;
        }
    }

    @Override
    public ExcelData update(ExcelData excelData) {
        excelData.setUpdatedAt(LocalDateTime.now());
        excelDataMapper.update(excelData);
        return excelData;
    }

    @Override
    public boolean deleteRow(Long excelId, Integer rowNumber, Long userId, String role) {
        // 检查文件访问权限
        if (!excelFileService.hasFileAccess(excelId, userId, role)) {
            throw new RuntimeException("Access denied");
        }

        return excelDataMapper.deleteByExcelIdAndRowNumber(excelId, rowNumber) > 0;
    }

    @Override
    public ExcelData addNewRow(Long excelId, Map<String, Object> cellData, Long userId) {
        // 获取当前最大行号
        int maxRowNumber = getMaxRowNumber(excelId);
        int newRowNumber = maxRowNumber + 1;

        return saveExcelData(excelId, newRowNumber, cellData, userId);
    }

    @Override
    public boolean deleteByExcelId(Long excelId, Long userId, String role) {
        // 检查文件访问权限
        if (!excelFileService.hasFileAccess(excelId, userId, role)) {
            throw new RuntimeException("Access denied");
        }

        return excelDataMapper.deleteByExcelId(excelId) > 0;
    }

    @Override
    public boolean importData(Long excelId, List<Map<String, Object>> dataList, Long userId) {
        try {
            // 清除现有数据
            deleteByExcelId(excelId, userId, "ADMIN");

            // 导入新数据
            for (int i = 0; i < dataList.size(); i++) {
                saveExcelData(excelId, i + 1, dataList.get(i), userId);
            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public int countByExcelId(Long excelId) {
        return excelDataMapper.countByExcelId(excelId);
    }

    @Override
    public int getMaxRowNumber(Long excelId) {
        return excelDataMapper.getMaxRowNumber(excelId);
    }
}
