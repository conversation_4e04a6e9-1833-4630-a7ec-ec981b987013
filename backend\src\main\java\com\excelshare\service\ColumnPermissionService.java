package com.excelshare.service;

import com.excelshare.dto.ColumnPermissionRequest;
import com.excelshare.entity.ColumnPermission;

import java.util.List;
import java.util.Map;

/**
 * 列权限服务接口
 *
 * <AUTHOR> Team
 */
public interface ColumnPermissionService {

    /**
     * 获取Excel文件的列权限配置
     */
    List<ColumnPermission> getColumnPermissions(Long excelId, Long userId, String role);

    /**
     * 设置列权限
     */
    ColumnPermission setColumnPermission(ColumnPermissionRequest request, Long userId, String role);

    /**
     * 批量设置列权限
     */
    void batchSetColumnPermissions(Long excelId, Map<String, Boolean> permissions, Long userId, String role);

    /**
     * 检查列是否可编辑
     */
    boolean isColumnEditable(Long excelId, String columnName, Long userId, String role);

    /**
     * 删除Excel文件的所有列权限
     */
    void deleteColumnPermissionsByExcelId(Long excelId, Long userId, String role);

    /**
     * 获取用户可编辑的列列表
     */
    List<String> getEditableColumns(Long excelId, Long userId, String role);
}
