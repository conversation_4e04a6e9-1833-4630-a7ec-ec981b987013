package com.excelshare.mapper;

import com.excelshare.entity.ColumnPermission;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 列权限Mapper接口
 *
 * <AUTHOR> Team
 */
@Mapper
public interface ColumnPermissionMapper {

    /**
     * 根据Excel文件ID查询列权限
     */
    @Select("SELECT * FROM column_permissions WHERE excel_id = #{excelId} ORDER BY column_name")
    List<ColumnPermission> findByExcelId(Long excelId);

    /**
     * 根据Excel文件ID和列名查询权限
     */
    @Select("SELECT * FROM column_permissions WHERE excel_id = #{excelId} AND column_name = #{columnName}")
    ColumnPermission findByExcelIdAndColumnName(Long excelId, String columnName);

    /**
     * 删除指定Excel文件的所有列权限
     */
    @Delete("DELETE FROM column_permissions WHERE excel_id = #{excelId}")
    void deleteByExcelId(Long excelId);

    /**
     * 根据用户ID和Excel文件ID查询权限
     */
    @Select("SELECT * FROM column_permissions WHERE excel_id = #{excelId} AND user_id = #{userId}")
    List<ColumnPermission> findByExcelIdAndUserId(Long excelId, Long userId);
}
