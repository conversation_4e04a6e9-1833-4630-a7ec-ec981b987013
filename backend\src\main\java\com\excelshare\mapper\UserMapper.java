package com.excelshare.mapper;

import com.excelshare.entity.User;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 用户数据访问层
 * 
 * <AUTHOR> Team
 */
@Mapper
public interface UserMapper {

    /**
     * 根据ID查询用户
     */
    @Select("SELECT * FROM users WHERE id = #{id} AND deleted = 0")
    User findById(@Param("id") Long id);

    /**
     * 根据用户名查询用户
     */
    @Select("SELECT * FROM users WHERE username = #{username} AND deleted = 0")
    User findByUsername(@Param("username") String username);

    /**
     * 根据邮箱查询用户
     */
    @Select("SELECT * FROM users WHERE email = #{email} AND deleted = 0")
    User findByEmail(@Param("email") String email);

    /**
     * 检查用户名是否存在
     */
    @Select("SELECT COUNT(*) FROM users WHERE username = #{username} AND deleted = 0")
    int countByUsername(@Param("username") String username);

    /**
     * 检查邮箱是否存在
     */
    @Select("SELECT COUNT(*) FROM users WHERE email = #{email} AND deleted = 0")
    int countByEmail(@Param("email") String email);

    /**
     * 插入新用户
     */
    @Insert("INSERT INTO users (username, password, email, role, created_at, updated_at, deleted) " +
            "VALUES (#{username}, #{password}, #{email}, #{role}, #{createdAt}, #{updatedAt}, #{deleted})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(User user);

    /**
     * 更新用户信息
     */
    @Update("UPDATE users SET username = #{username}, email = #{email}, role = #{role}, " +
            "updated_at = #{updatedAt} WHERE id = #{id}")
    int update(User user);

    /**
     * 更新用户密码
     */
    @Update("UPDATE users SET password = #{password}, updated_at = #{updatedAt} WHERE id = #{id}")
    int updatePassword(@Param("id") Long id, @Param("password") String password, 
                      @Param("updatedAt") java.time.LocalDateTime updatedAt);

    /**
     * 软删除用户
     */
    @Update("UPDATE users SET deleted = 1, updated_at = #{updatedAt} WHERE id = #{id}")
    int deleteById(@Param("id") Long id, @Param("updatedAt") java.time.LocalDateTime updatedAt);

    /**
     * 查询所有用户（分页）
     */
    @Select("SELECT * FROM users WHERE deleted = 0 ORDER BY created_at DESC LIMIT #{offset}, #{limit}")
    List<User> findAll(@Param("offset") int offset, @Param("limit") int limit);

    /**
     * 统计用户总数
     */
    @Select("SELECT COUNT(*) FROM users WHERE deleted = 0")
    int count();
}
