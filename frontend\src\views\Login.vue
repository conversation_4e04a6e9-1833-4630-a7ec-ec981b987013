<template>
  <div class="login-container">
    <el-card class="login-card" shadow="hover">
      <div class="login-header">
        <h2>Excel共享平台</h2>
        <p>请登录您的账户</p>
      </div>
      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        status-icon
        label-width="80px"
        class="login-form"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="loginForm.username" placeholder="请输入用户名"></el-input>
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
          ></el-input>
        </el-form-item>
        <el-form-item class="form-actions">
          <el-button type="primary" @click="handleLogin" :loading="loading">登录</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
        <div class="register-link">
          <span>还没有账户？</span>
          <el-link type="primary" @click="goToRegister">立即注册</el-link>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { useUserStore } from '../stores/user';
import { ElMessage } from 'element-plus';

const router = useRouter();
const userStore = useUserStore();
const loading = ref(false);
const loginForm = reactive({
  username: '',
  password: ''
});

const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于 6 个字符', trigger: 'blur' }
  ]
};

const loginFormRef = ref(null);

const resetForm = () => {
  loginFormRef.value.resetFields();
};

const handleLogin = async () => {
  try {
    await loginFormRef.value.validate();
    loading.value = true;

    const result = await userStore.login(loginForm);
    if (result.success) {
      ElMessage.success('登录成功');
      // 根据用户角色重定向
      if (userStore.currentUser.role === 'ADMIN') {
        router.push('/admin/dashboard');
      } else {
        router.push('/excel');
      }
    } else {
      ElMessage.error('用户名或密码错误');
    }
  } catch (error) {
    ElMessage.error(error.message || '登录失败，请重试');
  } finally {
    loading.value = false;
  }
};

const goToRegister = () => {
  router.push('/register');
};
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 20px;
  box-sizing: border-box;
}

.login-card {
  width: 100%;
  max-width: 400px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.login-header {
  text-align: center;
  margin-bottom: 25px;
  padding: 20px 20px 0;
}

.login-header h2 {
  color: #1890ff;
  margin-bottom: 5px;
  font-size: 24px;
}

.login-header p {
  color: #606266;
  margin: 0;
  font-size: 14px;
}

.login-form {
  padding: 0 20px 20px;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  gap: 10px;
}

.form-actions .el-button {
  flex: 1;
}

.register-link {
  margin-top: 15px;
  text-align: center;
  font-size: 14px;
}

.register-link span {
  color: #606266;
}

/* 响应式调整 */
@media (max-width: 480px) {
  .login-container {
    padding: 15px;
  }

  .login-card {
    border-radius: 8px;
  }

  .login-header {
    padding: 15px 15px 0;
    margin-bottom: 20px;
  }

  .login-header h2 {
    font-size: 20px;
  }

  .login-form {
    padding: 0 15px 15px;
  }

  .form-actions {
    flex-direction: column;
    gap: 8px;
  }

  .form-actions .el-button {
    width: 100%;
  }
}

@media (max-width: 360px) {
  .login-container {
    padding: 10px;
  }

  .login-header {
    padding: 10px 10px 0;
  }

  .login-form {
    padding: 0 10px 10px;
  }
}
</style>