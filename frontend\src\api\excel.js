import api from './index'

// Excel文件管理相关API
export const excelAPI = {
  // 获取Excel文件列表
  getFileList(params = {}) {
    return api.get('/excel-files', { params })
  },

  // 根据ID获取Excel文件信息
  getFileById(id) {
    return api.get(`/excel-files/${id}`)
  },

  // 上传Excel文件
  uploadFile(formData) {
    return api.post('/excel-files/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 下载Excel文件
  downloadFile(id) {
    return api.get(`/excel-files/${id}/download`, {
      responseType: 'blob'
    })
  },

  // 删除Excel文件
  deleteFile(id) {
    return api.delete(`/excel-files/${id}`)
  },

  // 获取Excel数据
  getExcelData(excelId) {
    return api.get(`/excel-data/${excelId}`)
  },

  // 保存Excel数据
  saveExcelData(data) {
    return api.post('/excel-data/save', data)
  },

  // 删除Excel数据行
  deleteExcelDataRow(excelId, rowNumber) {
    return api.delete(`/excel-data/${excelId}/row/${rowNumber}`)
  },

  // 添加新行
  addNewRow(excelId, cellData) {
    return api.post(`/excel-data/${excelId}/add-row`, cellData)
  },

  // 获取Excel文件列信息
  getExcelColumns(excelId) {
    return api.get(`/excel-data/${excelId}/columns`)
  },

  // 从Excel文件导入数据
  importDataFromExcel(excelId) {
    return api.post(`/excel-data/${excelId}/import`)
  }
}
