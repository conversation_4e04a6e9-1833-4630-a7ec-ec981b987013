package com.excelshare.service;

import com.excelshare.entity.User;

import java.util.List;

/**
 * 用户服务接口
 * 
 * <AUTHOR> Team
 */
public interface UserService {

    /**
     * 根据ID查询用户
     */
    User findById(Long id);

    /**
     * 根据用户名查询用户
     */
    User findByUsername(String username);

    /**
     * 根据邮箱查询用户
     */
    User findByEmail(String email);

    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(String username);

    /**
     * 检查邮箱是否存在
     */
    boolean existsByEmail(String email);

    /**
     * 保存用户
     */
    User save(User user);

    /**
     * 更新用户信息
     */
    User update(User user);

    /**
     * 更新用户密码
     */
    boolean updatePassword(Long id, String newPassword);

    /**
     * 删除用户
     */
    boolean deleteById(Long id);

    /**
     * 分页查询用户列表
     */
    List<User> findAll(int page, int size);

    /**
     * 统计用户总数
     */
    int count();
}
