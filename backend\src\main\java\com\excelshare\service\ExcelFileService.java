package com.excelshare.service;

import com.excelshare.entity.ExcelFile;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * Excel文件服务接口
 * 
 * <AUTHOR> Team
 */
public interface ExcelFileService {

    /**
     * 根据ID查询Excel文件
     */
    ExcelFile findById(Long id);

    /**
     * 根据创建者查询Excel文件列表
     */
    List<ExcelFile> findByCreatedBy(Long createdBy);

    /**
     * 查询所有Excel文件
     */
    List<ExcelFile> findAll();

    /**
     * 根据文件名搜索Excel文件
     */
    List<ExcelFile> searchByFileName(String fileName, Long userId, String role);

    /**
     * 上传Excel文件
     */
    ExcelFile uploadFile(MultipartFile file, String fileName, String description, Long userId);

    /**
     * 保存Excel文件信息
     */
    ExcelFile save(ExcelFile excelFile);

    /**
     * 更新Excel文件信息
     */
    ExcelFile update(ExcelFile excelFile);

    /**
     * 删除Excel文件
     */
    boolean deleteById(Long id);

    /**
     * 检查用户是否有文件访问权限
     */
    boolean hasFileAccess(Long fileId, Long userId, String role);

    /**
     * 下载Excel文件
     */
    byte[] downloadFile(Long fileId, Long userId, String role);

    /**
     * 统计用户的Excel文件数量
     */
    int countByCreatedBy(Long createdBy);

    /**
     * 统计所有Excel文件数量
     */
    int count();
}
