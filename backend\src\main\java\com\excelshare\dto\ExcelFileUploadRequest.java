package com.excelshare.dto;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * Excel文件上传请求DTO
 * 
 * <AUTHOR> Team
 */
@Data
public class ExcelFileUploadRequest {
    
    @NotBlank(message = "文件名不能为空")
    @Size(max = 255, message = "文件名长度不能超过255个字符")
    private String fileName;
    
    @Size(max = 1000, message = "描述长度不能超过1000个字符")
    private String description;
}
