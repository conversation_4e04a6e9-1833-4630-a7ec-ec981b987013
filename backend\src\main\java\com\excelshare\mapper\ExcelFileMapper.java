package com.excelshare.mapper;

import com.excelshare.entity.ExcelFile;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * Excel文件数据访问层
 * 
 * <AUTHOR> Team
 */
@Mapper
public interface ExcelFileMapper {

    /**
     * 根据ID查询Excel文件
     */
    @Select("SELECT * FROM excel_files WHERE id = #{id} AND deleted = 0")
    ExcelFile findById(@Param("id") Long id);

    /**
     * 根据创建者查询Excel文件列表
     */
    @Select("SELECT * FROM excel_files WHERE created_by = #{createdBy} AND deleted = 0 ORDER BY created_at DESC")
    List<ExcelFile> findByCreatedBy(@Param("createdBy") Long createdBy);

    /**
     * 查询所有Excel文件
     */
    @Select("SELECT * FROM excel_files WHERE deleted = 0 ORDER BY created_at DESC")
    List<ExcelFile> findAll();

    /**
     * 根据文件名搜索Excel文件
     */
    @Select("SELECT * FROM excel_files WHERE file_name LIKE CONCAT('%', #{fileName}, '%') AND deleted = 0 ORDER BY created_at DESC")
    List<ExcelFile> findByFileNameLike(@Param("fileName") String fileName);

    /**
     * 根据创建者和文件名搜索Excel文件
     */
    @Select("SELECT * FROM excel_files WHERE created_by = #{createdBy} AND file_name LIKE CONCAT('%', #{fileName}, '%') AND deleted = 0 ORDER BY created_at DESC")
    List<ExcelFile> findByCreatedByAndFileNameLike(@Param("createdBy") Long createdBy, @Param("fileName") String fileName);

    /**
     * 插入新的Excel文件
     */
    @Insert("INSERT INTO excel_files (file_name, description, file_path, created_by, created_at, updated_at, deleted) " +
            "VALUES (#{fileName}, #{description}, #{filePath}, #{createdBy}, #{createdAt}, #{updatedAt}, #{deleted})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(ExcelFile excelFile);

    /**
     * 更新Excel文件信息
     */
    @Update("UPDATE excel_files SET file_name = #{fileName}, description = #{description}, " +
            "updated_at = #{updatedAt} WHERE id = #{id}")
    int update(ExcelFile excelFile);

    /**
     * 软删除Excel文件
     */
    @Update("UPDATE excel_files SET deleted = 1, updated_at = #{updatedAt} WHERE id = #{id}")
    int deleteById(@Param("id") Long id, @Param("updatedAt") java.time.LocalDateTime updatedAt);

    /**
     * 统计用户的Excel文件数量
     */
    @Select("SELECT COUNT(*) FROM excel_files WHERE created_by = #{createdBy} AND deleted = 0")
    int countByCreatedBy(@Param("createdBy") Long createdBy);

    /**
     * 统计所有Excel文件数量
     */
    @Select("SELECT COUNT(*) FROM excel_files WHERE deleted = 0")
    int count();
}
