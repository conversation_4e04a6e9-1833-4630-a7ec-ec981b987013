package com.excelshare.service.impl;

import com.excelshare.entity.ExcelFile;
import com.excelshare.mapper.ExcelFileMapper;
import com.excelshare.service.ExcelFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Excel文件服务实现类 - 数据库版本
 *
 * <AUTHOR> Team
 */
@Service
public class ExcelFileServiceImpl implements ExcelFileService {

    @Autowired
    private ExcelFileMapper excelFileMapper;

    private final String uploadPath = "./uploads/excel/";

    public ExcelFileServiceImpl() {
        createUploadDirectory();
    }

    private void createUploadDirectory() {
        try {
            Path uploadDir = Paths.get(uploadPath);
            if (!Files.exists(uploadDir)) {
                Files.createDirectories(uploadDir);
            }
        } catch (IOException e) {
            System.err.println("Failed to create upload directory: " + e.getMessage());
        }
    }

    @Override
    public ExcelFile findById(Long id) {
        return excelFileMapper.findById(id);
    }

    @Override
    public List<ExcelFile> findByCreatedBy(Long createdBy) {
        return excelFileMapper.findByCreatedBy(createdBy);
    }

    @Override
    public List<ExcelFile> findAll() {
        return excelFileMapper.findAll();
    }

    @Override
    public List<ExcelFile> searchByFileName(String fileName, Long userId, String role) {
        if ("ADMIN".equals(role)) {
            if (fileName != null && !fileName.trim().isEmpty()) {
                return excelFileMapper.findByFileNameLike(fileName);
            } else {
                return excelFileMapper.findAll();
            }
        } else {
            if (fileName != null && !fileName.trim().isEmpty()) {
                return excelFileMapper.findByCreatedByAndFileNameLike(userId, fileName);
            } else {
                return excelFileMapper.findByCreatedBy(userId);
            }
        }
    }

    @Override
    public ExcelFile uploadFile(MultipartFile file, String fileName, String description, Long userId) {
        try {
            // 生成唯一文件名
            String originalFilename = file.getOriginalFilename();
            String extension = originalFilename != null && originalFilename.contains(".")
                    ? originalFilename.substring(originalFilename.lastIndexOf("."))
                    : ".xlsx";
            String uniqueFileName = System.currentTimeMillis() + "_" + fileName + extension;

            // 保存文件
            Path filePath = Paths.get(uploadPath + uniqueFileName);
            Files.write(filePath, file.getBytes());

            // 创建文件记录
            ExcelFile excelFile = new ExcelFile();
            excelFile.setFileName(fileName + extension);
            excelFile.setDescription(description);
            excelFile.setFilePath("/uploads/excel/" + uniqueFileName);
            excelFile.setCreatedBy(userId);
            excelFile.setCreatedAt(LocalDateTime.now());
            excelFile.setUpdatedAt(LocalDateTime.now());
            excelFile.setDeleted(false);

            excelFileMapper.insert(excelFile);
            return excelFile;

        } catch (IOException e) {
            throw new RuntimeException("Failed to upload file: " + e.getMessage(), e);
        }
    }

    @Override
    public ExcelFile save(ExcelFile excelFile) {
        if (excelFile.getId() == null) {
            excelFile.setCreatedAt(LocalDateTime.now());
            excelFile.setUpdatedAt(LocalDateTime.now());
            excelFile.setDeleted(false);
            excelFileMapper.insert(excelFile);
        } else {
            excelFile.setUpdatedAt(LocalDateTime.now());
            excelFileMapper.update(excelFile);
        }
        return excelFile;
    }

    @Override
    public ExcelFile update(ExcelFile excelFile) {
        excelFile.setUpdatedAt(LocalDateTime.now());
        excelFileMapper.update(excelFile);
        return excelFile;
    }

    @Override
    public boolean deleteById(Long id) {
        return excelFileMapper.deleteById(id, LocalDateTime.now()) > 0;
    }

    @Override
    public boolean hasFileAccess(Long fileId, Long userId, String role) {
        if ("ADMIN".equals(role)) {
            return true;
        }
        
        ExcelFile file = findById(fileId);
        return file != null && userId.equals(file.getCreatedBy());
    }

    @Override
    public byte[] downloadFile(Long fileId, Long userId, String role) {
        if (!hasFileAccess(fileId, userId, role)) {
            throw new RuntimeException("Access denied");
        }
        
        ExcelFile file = findById(fileId);
        if (file == null) {
            throw new RuntimeException("File not found");
        }
        
        try {
            Path filePath = Paths.get("." + file.getFilePath());
            if (Files.exists(filePath)) {
                return Files.readAllBytes(filePath);
            } else {
                // 返回模拟的Excel文件内容
                return createMockExcelContent(file.getFileName());
            }
        } catch (IOException e) {
            throw new RuntimeException("Failed to download file: " + e.getMessage(), e);
        }
    }

    private byte[] createMockExcelContent(String fileName) {
        // 返回一个简单的文本内容作为模拟
        String content = "Mock Excel Content for: " + fileName + "\n";
        content += "This is a simulated file download.\n";
        content += "In a real implementation, this would be actual Excel data.";
        return content.getBytes();
    }

    @Override
    public int countByCreatedBy(Long createdBy) {
        return excelFileMapper.countByCreatedBy(createdBy);
    }

    @Override
    public int count() {
        return excelFileMapper.count();
    }
}
