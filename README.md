# Excel共享平台

一个基于Spring Boot + Vue.js的Excel文件共享和协作编辑平台。

## 🚀 项目特性

- **用户管理**: 支持用户注册、登录、权限管理
- **文件上传**: 支持Excel文件上传和管理
- **在线编辑**: 支持Excel数据的在线查看和编辑
- **权限控制**: 支持列级别的编辑权限控制
- **数据导入导出**: 支持Excel数据的导入和导出
- **安全认证**: 基于JWT的安全认证机制

## 🛠️ 技术栈

### 后端
- **Spring Boot 3.2.0**: 主框架
- **Spring Security**: 安全认证
- **MyBatis-Plus**: ORM框架
- **MySQL**: 数据库
- **Apache POI**: Excel文件处理
- **JWT**: 身份认证
- **Maven**: 项目管理

### 前端
- **Vue.js 3**: 前端框架
- **Element Plus**: UI组件库
- **Axios**: HTTP客户端
- **Vue Router**: 路由管理
- **Pinia**: 状态管理

## 📁 项目结构

```
excel-share-platform/
├── backend/                 # 后端项目
│   ├── src/main/java/
│   │   └── com/excelshare/
│   │       ├── controller/  # 控制器层
│   │       ├── service/     # 服务层
│   │       ├── entity/      # 实体类
│   │       ├── mapper/      # 数据访问层
│   │       ├── dto/         # 数据传输对象
│   │       ├── config/      # 配置类
│   │       ├── security/    # 安全配置
│   │       ├── util/        # 工具类
│   │       ├── common/      # 通用类
│   │       └── exception/   # 异常处理
│   └── src/main/resources/
│       ├── application.yml  # 应用配置
│       └── sql/            # 数据库脚本
└── frontend/               # 前端项目
    ├── src/
    │   ├── components/     # 组件
    │   ├── views/         # 页面
    │   ├── router/        # 路由
    │   ├── store/         # 状态管理
    │   ├── api/           # API接口
    │   └── utils/         # 工具函数
    └── public/            # 静态资源
```

## 🚀 快速开始

### 环境要求

- Java 17+
- Node.js 16+
- MySQL 8.0+
- Maven 3.6+

### 后端启动

1. **创建数据库**
   ```sql
   CREATE DATABASE excel_share_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

2. **执行数据库脚本**
   ```bash
   mysql -u root -p excel_share_platform < backend/src/main/resources/sql/init.sql
   ```

3. **修改配置文件**
   编辑 `backend/src/main/resources/application.yml`，配置数据库连接信息：
   ```yaml
   spring:
     datasource:
       url: ************************************************
       username: root
       password: your_password
   ```

4. **启动后端服务**
   ```bash
   cd backend
   mvn spring-boot:run
   ```

   服务启动后访问: http://localhost:8080

### 前端启动

1. **安装依赖**
   ```bash
   cd frontend
   npm install
   ```

2. **启动开发服务器**
   ```bash
   npm run dev
   ```

   前端启动后访问: http://localhost:5173

## 📚 API文档

### 认证接口

- `POST /api/auth/login` - 用户登录
- `POST /api/auth/register` - 用户注册

### 用户管理

- `GET /api/users/current` - 获取当前用户信息
- `GET /api/users` - 获取用户列表（管理员）
- `POST /api/users` - 创建用户（管理员）
- `PUT /api/users/{id}` - 更新用户（管理员）
- `DELETE /api/users/{id}` - 删除用户（管理员）

### Excel文件管理

- `POST /api/excel-files/upload` - 上传Excel文件
- `GET /api/excel-files` - 获取文件列表
- `GET /api/excel-files/{id}` - 获取文件信息
- `GET /api/excel-files/{id}/download` - 下载文件
- `DELETE /api/excel-files/{id}` - 删除文件

### Excel数据操作

- `GET /api/excel-data/{excelId}` - 获取Excel数据
- `POST /api/excel-data/save` - 保存Excel数据
- `POST /api/excel-data/{excelId}/add-row` - 添加新行
- `DELETE /api/excel-data/{excelId}/row/{rowNumber}` - 删除行
- `GET /api/excel-data/{excelId}/export` - 导出数据

### 权限管理

- `GET /api/column-permissions/{excelId}` - 获取列权限
- `POST /api/column-permissions` - 设置列权限（管理员）
- `GET /api/column-permissions/{excelId}/editable` - 获取可编辑列

## 🔧 配置说明

### 数据库配置
```yaml
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ************************************************
    username: root
    password: 123456
```

### JWT配置
```yaml
jwt:
  secret: mySecretKey
  expiration: 86400000  # 24小时
```

### 文件上传配置
```yaml
file:
  upload-path: ./uploads/excel/
```

## 🔐 安全特性

- JWT身份认证
- 基于角色的访问控制（RBAC）
- 列级别权限控制
- CORS跨域支持
- 请求参数验证

## 🐛 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查MySQL服务是否启动
   - 确认数据库连接配置正确
   - 检查防火墙设置

2. **文件上传失败**
   - 检查上传目录权限
   - 确认文件大小限制
   - 检查磁盘空间

3. **前端无法访问后端**
   - 检查CORS配置
   - 确认后端服务正常启动
   - 检查网络连接

## 📄 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 👥 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 联系方式

如有问题，请联系开发团队。
