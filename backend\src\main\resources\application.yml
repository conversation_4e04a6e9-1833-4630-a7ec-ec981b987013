server:
  port: 8080

spring:
  application:
    name: excel-share-platform

  # 数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************************************************
    username: root
    password: 123456

  # SQL初始化配置 - 禁用自动初始化，使用手动执行init_database.sql
  sql:
    init:
      mode: never
        
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
      
  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8

# MyBatis 配置
mybatis:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  type-handlers-package: com.excelshare.config

# JWT配置
jwt:
  secret: excelSharePlatformSecretKey2024abcdefghijklmnopqrstuvwxyz1234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ
  expiration: 86400000 # 24小时

# 文件存储配置
file:
  upload-path: ./uploads/excel/
  
# 日志配置
logging:
  level:
    com.excelshare: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  charset:
    console: UTF-8
    file: UTF-8
