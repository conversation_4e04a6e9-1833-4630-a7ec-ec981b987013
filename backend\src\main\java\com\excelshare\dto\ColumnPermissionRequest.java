package com.excelshare.dto;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 列权限配置请求DTO
 * 
 * <AUTHOR> Team
 */
@Data
public class ColumnPermissionRequest {
    
    @NotNull(message = "Excel文件ID不能为空")
    private Long excelId;
    
    @NotBlank(message = "列名不能为空")
    private String columnName;
    
    @NotNull(message = "是否可编辑不能为空")
    private Boolean editable;
}
