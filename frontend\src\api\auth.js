import api from './index'

// 用户认证相关API
export const authAPI = {
  // 用户登录
  login(data) {
    return api.post('/auth/login', data)
  },

  // 用户注册
  register(data) {
    return api.post('/auth/register', data)
  },

  // 获取当前用户信息
  getCurrentUser() {
    return api.get('/auth/current')
  },

  // 登出（前端处理）
  logout() {
    // 清除本地存储的token等信息
    localStorage.removeItem('token')
    localStorage.removeItem('user')
  }
}
