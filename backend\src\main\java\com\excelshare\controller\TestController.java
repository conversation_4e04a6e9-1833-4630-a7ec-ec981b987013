package com.excelshare.controller;

import com.excelshare.common.Result;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 测试控制器
 * 用于验证基础功能
 * 
 * <AUTHOR> Team
 */
@RestController
@RequestMapping("/api/test")
@CrossOrigin(origins = "*")
public class TestController {

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Result<Map<String, Object>> health() {
        Map<String, Object> data = new HashMap<>();
        data.put("status", "UP");
        data.put("timestamp", LocalDateTime.now());
        data.put("service", "Excel共享平台");
        data.put("version", "1.0.0");
        return Result.success("服务正常运行", data);
    }

    /**
     * 测试CORS
     */
    @GetMapping("/cors")
    public Result<String> testCors() {
        return Result.success("CORS配置正常");
    }

    /**
     * 测试POST请求
     */
    @PostMapping("/echo")
    public Result<Map<String, Object>> echo(@RequestBody Map<String, Object> data) {
        Map<String, Object> response = new HashMap<>();
        response.put("received", data);
        response.put("timestamp", LocalDateTime.now());
        return Result.success("数据接收成功", response);
    }
}
