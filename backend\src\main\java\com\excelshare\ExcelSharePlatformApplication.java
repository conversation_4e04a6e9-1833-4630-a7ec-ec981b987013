package com.excelshare;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * Excel共享平台主启动类
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */
@SpringBootApplication(exclude = {
    org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration.class,
    org.springframework.boot.autoconfigure.data.jpa.JpaRepositoriesAutoConfiguration.class
})
@MapperScan("com.excelshare.mapper")
public class ExcelSharePlatformApplication {

    public static void main(String[] args) {
        // 设置系统编码为UTF-8
        System.setProperty("file.encoding", "UTF-8");
        System.setProperty("sun.jnu.encoding", "UTF-8");
        System.setProperty("console.encoding", "UTF-8");

        SpringApplication.run(ExcelSharePlatformApplication.class, args);
        System.out.println("=================================");
        System.out.println("Excel共享平台启动成功！");
        System.out.println("访问地址: http://localhost:8080");
        System.out.println("=================================");
    }
}
