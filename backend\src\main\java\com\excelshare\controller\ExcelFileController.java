package com.excelshare.controller;

import com.excelshare.common.Result;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.*;

/**
 * Excel文件控制器 - 模拟版本
 * 
 * <AUTHOR> Team
 */
@RestController
@RequestMapping("/api/excel-files")
@CrossOrigin(origins = "*")
public class ExcelFileController {

    // 模拟文件存储
    private static final List<Map<String, Object>> mockFiles = new ArrayList<>();
    private static Long fileIdCounter = 1L;

    static {
        // 初始化一些模拟数据
        mockFiles.add(Map.of(
            "id", 1L,
            "fileName", "员工信息表.xlsx",
            "description", "公司员工基本信息",
            "createdBy", 1L,
            "createdByUsername", "admin",
            "createdAt", LocalDateTime.now().minusDays(2),
            "updatedAt", LocalDateTime.now().minusDays(2)
        ));
        mockFiles.add(Map.of(
            "id", 2L,
            "fileName", "销售数据.xlsx", 
            "description", "2024年销售数据统计",
            "createdBy", 2L,
            "createdByUsername", "user",
            "createdAt", LocalDateTime.now().minusDays(1),
            "updatedAt", LocalDateTime.now().minusDays(1)
        ));
        fileIdCounter = 3L;
    }

    /**
     * 上传Excel文件 - 模拟版本
     */
    @PostMapping("/upload")
    public Result<Map<String, Object>> uploadFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "fileName", required = false) String fileName,
            @RequestParam(value = "description", required = false) String description,
            @RequestHeader(value = "Authorization", required = false) String token) {
        
        if (token == null || !token.startsWith("Bearer ")) {
            return Result.unauthorized("未登录");
        }
        
        // 模拟文件上传
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || (!originalFilename.endsWith(".xlsx") && !originalFilename.endsWith(".xls"))) {
            return Result.badRequest("只支持Excel文件格式(.xlsx, .xls)");
        }
        
        Map<String, Object> newFile = new HashMap<>();
        newFile.put("id", fileIdCounter++);
        newFile.put("fileName", fileName != null ? fileName : originalFilename);
        newFile.put("description", description != null ? description : "");
        newFile.put("createdBy", token.contains("admin") ? 1L : 2L);
        newFile.put("createdByUsername", token.contains("admin") ? "admin" : "user");
        newFile.put("createdAt", LocalDateTime.now());
        newFile.put("updatedAt", LocalDateTime.now());
        
        mockFiles.add(newFile);
        
        return Result.success("文件上传成功", newFile);
    }

    /**
     * 获取Excel文件列表
     */
    @GetMapping
    public Result<List<Map<String, Object>>> getFileList(
            @RequestParam(required = false) String fileName,
            @RequestHeader(value = "Authorization", required = false) String token) {
        
        if (token == null || !token.startsWith("Bearer ")) {
            return Result.unauthorized("未登录");
        }
        
        List<Map<String, Object>> result = new ArrayList<>(mockFiles);
        
        // 如果是普通用户，只返回自己的文件
        if (!token.contains("admin")) {
            result = result.stream()
                    .filter(file -> "user".equals(file.get("createdByUsername")))
                    .toList();
        }
        
        // 文件名搜索
        if (fileName != null && !fileName.trim().isEmpty()) {
            result = result.stream()
                    .filter(file -> file.get("fileName").toString().contains(fileName.trim()))
                    .toList();
        }
        
        return Result.success(result);
    }

    /**
     * 根据ID获取Excel文件信息
     */
    @GetMapping("/{id}")
    public Result<Map<String, Object>> getFileById(
            @PathVariable Long id,
            @RequestHeader(value = "Authorization", required = false) String token) {
        
        if (token == null || !token.startsWith("Bearer ")) {
            return Result.unauthorized("未登录");
        }
        
        Optional<Map<String, Object>> file = mockFiles.stream()
                .filter(f -> id.equals(f.get("id")))
                .findFirst();
        
        if (file.isEmpty()) {
            return Result.notFound("文件不存在");
        }
        
        Map<String, Object> fileData = file.get();
        
        // 权限检查
        if (!token.contains("admin") && !"user".equals(fileData.get("createdByUsername"))) {
            return Result.forbidden("没有权限访问此文件");
        }
        
        return Result.success(fileData);
    }

    /**
     * 下载Excel文件 - 模拟版本
     */
    @GetMapping("/{id}/download")
    public ResponseEntity<byte[]> downloadFile(
            @PathVariable Long id,
            @RequestHeader(value = "Authorization", required = false) String token) {
        
        if (token == null || !token.startsWith("Bearer ")) {
            return ResponseEntity.status(401).build();
        }
        
        Optional<Map<String, Object>> file = mockFiles.stream()
                .filter(f -> id.equals(f.get("id")))
                .findFirst();
        
        if (file.isEmpty()) {
            return ResponseEntity.notFound().build();
        }
        
        // 模拟Excel文件内容
        String content = "模拟Excel文件内容 - " + file.get().get("fileName");
        byte[] fileContent = content.getBytes();
        
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, 
                        "attachment; filename=\"" + file.get().get("fileName") + "\"")
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(fileContent);
    }

    /**
     * 删除Excel文件
     */
    @DeleteMapping("/{id}")
    public Result<String> deleteFile(
            @PathVariable Long id,
            @RequestHeader(value = "Authorization", required = false) String token) {
        
        if (token == null || !token.startsWith("Bearer ")) {
            return Result.unauthorized("未登录");
        }
        
        Optional<Map<String, Object>> file = mockFiles.stream()
                .filter(f -> id.equals(f.get("id")))
                .findFirst();
        
        if (file.isEmpty()) {
            return Result.notFound("文件不存在");
        }
        
        Map<String, Object> fileData = file.get();
        
        // 权限检查
        if (!token.contains("admin") && !"user".equals(fileData.get("createdByUsername"))) {
            return Result.forbidden("没有权限删除此文件");
        }
        
        mockFiles.remove(fileData);
        return Result.success("文件删除成功");
    }
}
