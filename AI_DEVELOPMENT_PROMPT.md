# 🤖 AI开发助手提示词 - Excel共享平台

## 📋 项目背景
你正在协助开发一个**Excel共享平台**，这是一个基于Spring Boot + MyBatis + MySQL + Vue 3的Web应用。项目已经完成了大部分架构搭建，目前处于**数据库集成测试阶段**。

## 🎯 当前任务状态
**主要目标：** 完成MyBatis与MySQL的集成，实现真实数据库操作，替代之前的模拟数据

**技术栈：**
- 后端：Spring Boot 2.7.18 + MyBatis 2.3.1 + MySQL 8.0
- 前端：Vue 3 + Element Plus + Axios
- 认证：JWT + Spring Security

## 🔧 已解决的技术问题

### 1. MyBatis兼容性问题
- **问题：** MyBatis-Plus 3.5.x 与 Spring Boot 3.2.0 不兼容
- **解决方案：** 降级到Spring Boot 2.7.18 + 原生MyBatis 2.3.1
- **状态：** ✅ 已解决

### 2. 包名兼容性问题
- **问题：** Spring Boot 2.7.x 使用javax.*，不是jakarta.*
- **解决方案：** 修改所有validation和servlet相关import
- **状态：** ✅ 已解决

### 3. 实体类兼容性问题
- **问题：** 实体类中使用了jakarta.*包名和MyBatis-Plus注解
- **解决方案：** 重写实体类为简单POJO，移除所有JPA和MyBatis-Plus注解
- **状态：** ✅ 已解决

### 4. JWT密钥长度问题
- **问题：** 密钥长度不足256位
- **解决方案：** 扩展JWT密钥长度
- **状态：** ✅ 已解决

### 5. 临时文件清理
- **问题：** temp_controllers和temp_entities目录包含过期的jakarta代码
- **解决方案：** 删除临时目录，清理过期代码
- **状态：** ✅ 已解决

### 6. MyBatis Mapper兼容性修复
- **问题：** Mapper接口仍使用MyBatis-Plus的BaseMapper，导致编译失败
- **解决方案：** 移除所有MyBatis-Plus依赖，改为纯MyBatis注解方式
- **状态：** ✅ 已解决

### 7. SQL文件整理优化
- **问题：** 存在多个版本的SQL文件，结构不统一，容易混淆
- **解决方案：** 创建统一的完整数据库初始化文件
- **状态：** ✅ 已解决

## 📁 项目结构概览

### 核心文件位置
```
backend/
├── src/main/java/com/excelshare/
│   ├── ExcelSharePlatformApplication.java  # 主启动类
│   ├── entity/                             # 实体类 (User, ExcelFile, ExcelData)
│   ├── mapper/                             # MyBatis Mapper接口
│   ├── service/impl/                       # Service实现类 (数据库版本)
│   ├── controller/                         # REST API控制器
│   └── config/JsonTypeHandler.java         # JSON类型处理器
├── src/main/resources/
│   ├── application.yml                     # 应用配置 (MySQL配置)
│   ├── init_database.sql                   # 完整数据库初始化脚本
│   ├── schema.sql                          # 表结构（Spring Boot自动执行）
│   └── data.sql                            # 初始数据（Spring Boot自动执行）
└── pom.xml                                 # Maven依赖 (Spring Boot 2.7.18)

frontend/
├── src/
│   ├── views/                              # Vue页面组件
│   ├── api/                                # API接口封装
│   └── components/                         # 通用组件
└── package.json                            # 前端依赖
```

## 🚀 下一步开发任务

### 🔥 紧急任务 (优先级最高)

#### 1. 数据库连接验证
```bash
# 方式一：使用完整初始化脚本（推荐）
1. 确保MySQL服务运行
2. 执行完整初始化：mysql -u root -p < backend/src/main/resources/init_database.sql
3. 启动后端：cd backend && mvn spring-boot:run
4. 检查日志中的数据库连接状态

# 方式二：让Spring Boot自动创建
1. 确保MySQL服务运行
2. 手动创建数据库：CREATE DATABASE excel_share_platform;
3. 启动后端：cd backend && mvn spring-boot:run
4. Spring Boot会自动执行schema.sql和data.sql
```

#### 2. API功能测试
```bash
# 测试用例：
1. 健康检查：GET http://localhost:8080/api/simple/health
2. 用户登录：POST http://localhost:8080/api/auth/login
   - 测试账号：admin/123456, user/123456
3. 文件列表：GET http://localhost:8080/api/excel-files
4. Excel数据：GET http://localhost:8080/api/excel-data/1
```

#### 3. 前后端联调
```bash
# 启动步骤：
1. 后端：cd backend && mvn spring-boot:run (端口8080)
2. 前端：cd frontend && npm run dev (端口5173)
3. 测试完整业务流程
```

## 🔍 关键配置信息

### 数据库配置 (application.yml)
```yaml
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************************************************
    username: root
    password: 123456
```

### MyBatis配置
```yaml
mybatis:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  type-handlers-package: com.excelshare.config
```

### 默认用户账号
- 管理员：admin / 123456
- 普通用户：user / 123456
- 测试用户：test / 123456

## 🐛 可能遇到的问题及解决方案

### 1. 数据库连接失败
**症状：** 启动时报数据库连接错误
**解决：**
- 检查MySQL服务是否启动
- 验证数据库名、用户名、密码
- 确认端口3306可访问

### 2. 表创建失败
**症状：** schema.sql执行失败
**解决：**
- 检查SQL语法兼容性
- 验证外键约束
- 确认字符集设置

### 3. JSON数据处理错误
**症状：** cell_data字段序列化/反序列化失败
**解决：**
- 检查JsonTypeHandler配置
- 验证MySQL JSON字段支持
- 确认数据格式正确

## 💡 开发建议

### 1. 调试策略
- 启用MyBatis SQL日志输出
- 使用Postman/curl测试API
- 检查浏览器开发者工具网络请求

### 2. 错误处理
- 查看后端控制台日志
- 检查前端浏览器控制台
- 使用数据库客户端验证数据

### 3. 性能优化
- 监控SQL查询性能
- 检查数据库索引使用
- 优化大文件上传处理

## 🎯 成功标准

### 阶段一：数据库集成成功
- [ ] 后端成功启动，无数据库连接错误
- [ ] 数据库表正确创建，包含初始数据（3用户、3文件、示例数据）
- [ ] MyBatis Mapper正常工作
- [ ] 数据库初始化验证查询正常显示

### 阶段二：API功能验证
- [ ] 用户登录成功，返回JWT token
- [ ] 文件列表API返回数据库中的文件
- [ ] Excel数据CRUD操作正常

### 阶段三：前后端联调
- [ ] 前端成功调用后端API
- [ ] 用户可以正常登录和操作
- [ ] 文件上传下载功能正常

## 🔄 开发流程

### 每次开发开始时：
1. 检查DEVELOPMENT_PROGRESS.md了解最新状态
2. 确认MySQL服务运行状态
3. 启动后端服务，检查启动日志
4. 运行基础API测试验证功能

### 遇到问题时：
1. 查看详细错误日志
2. 检查相关配置文件
3. 验证数据库状态
4. 逐步排查问题根因

### 完成任务后：
1. 更新DEVELOPMENT_PROGRESS.md
2. 记录解决的问题和方案
3. 标记完成的任务
4. 规划下一步工作

## � 数据库文件说明

### SQL文件结构
- **`init_database.sql`** - 完整的数据库初始化脚本
  - 包含完整的表结构定义
  - 包含3个默认用户、3个示例文件、示例数据和权限配置
  - 包含数据验证查询
  - 适用于手动执行完整初始化

- **`schema.sql`** - 表结构文件（Spring Boot自动执行）
- **`data.sql`** - 初始数据文件（Spring Boot自动执行）

### 使用建议
- **开发环境：** 使用 `init_database.sql` 手动初始化（推荐）
- **生产环境：** 使用 `schema.sql` + `data.sql` 让Spring Boot自动处理

## �📞 重要提醒

- **数据库名：** excel_share_platform
- **后端端口：** 8080
- **前端端口：** 5173
- **Spring Boot版本：** 2.7.18 (不要升级到3.x)
- **MyBatis版本：** 2.3.1 (不要使用MyBatis-Plus)
- **编译状态：** ✅ 已通过，可直接启动测试

---
**AI助手使用说明：** 
1. 优先完成数据库连接测试
2. 遇到兼容性问题时参考已解决的问题
3. 保持Spring Boot 2.7.x版本不变
4. 及时更新开发进度文档
