package com.excelshare.controller;

import com.excelshare.common.Result;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 认证控制器 - 简化版本
 * 
 * <AUTHOR> Team
 */
@RestController
@RequestMapping("/api/auth")
@CrossOrigin(origins = "*")
public class AuthController {

    /**
     * 用户登录 - 模拟版本
     */
    @PostMapping("/login")
    public Result<Map<String, Object>> login(@RequestBody Map<String, String> request) {
        String username = request.get("username");
        String password = request.get("password");
        
        // 简单验证 - 演示用
        if ("admin".equals(username) && "123456".equals(password)) {
            Map<String, Object> data = new HashMap<>();
            data.put("token", "mock-jwt-token-admin");
            data.put("user", Map.of(
                "id", 1,
                "username", "admin",
                "email", "<EMAIL>",
                "role", "ADMIN"
            ));
            return Result.success("登录成功", data);
        } else if ("user".equals(username) && "123456".equals(password)) {
            Map<String, Object> data = new HashMap<>();
            data.put("token", "mock-jwt-token-user");
            data.put("user", Map.of(
                "id", 2,
                "username", "user",
                "email", "<EMAIL>",
                "role", "USER"
            ));
            return Result.success("登录成功", data);
        } else {
            return Result.badRequest("用户名或密码错误");
        }
    }

    /**
     * 用户注册 - 模拟版本
     */
    @PostMapping("/register")
    public Result<Map<String, Object>> register(@RequestBody Map<String, String> request) {
        String username = request.get("username");
        String email = request.get("email");
        
        // 模拟注册成功
        Map<String, Object> user = Map.of(
            "id", 3,
            "username", username,
            "email", email,
            "role", "USER"
        );
        
        return Result.success("注册成功", user);
    }

    /**
     * 获取当前用户信息 - 模拟版本
     */
    @GetMapping("/current")
    public Result<Map<String, Object>> getCurrentUser(@RequestHeader(value = "Authorization", required = false) String token) {
        if (token == null || !token.startsWith("Bearer ")) {
            return Result.unauthorized("未登录");
        }
        
        // 根据token返回不同用户信息
        if (token.contains("admin")) {
            Map<String, Object> user = Map.of(
                "id", 1,
                "username", "admin",
                "email", "<EMAIL>",
                "role", "ADMIN"
            );
            return Result.success(user);
        } else {
            Map<String, Object> user = Map.of(
                "id", 2,
                "username", "user",
                "email", "<EMAIL>",
                "role", "USER"
            );
            return Result.success(user);
        }
    }
}
