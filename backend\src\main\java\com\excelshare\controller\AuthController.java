package com.excelshare.controller;

import com.excelshare.common.Result;
import com.excelshare.dto.LoginRequest;
import com.excelshare.dto.LoginResponse;
import com.excelshare.dto.RegisterRequest;
import com.excelshare.entity.User;
import com.excelshare.service.UserService;
import com.excelshare.util.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * 认证控制器 - 数据库版本
 *
 * <AUTHOR> Team
 */
@RestController
@RequestMapping("/api/auth")
@CrossOrigin(origins = "*")
public class AuthController {

    @Autowired
    private UserService userService;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private JwtUtil jwtUtil;

    /**
     * 用户登录
     */
    @PostMapping("/login")
    public Result<LoginResponse> login(@Valid @RequestBody LoginRequest request) {
        try {
            // 查找用户
            User user = userService.findByUsername(request.getUsername());
            if (user == null) {
                return Result.badRequest("用户名或密码错误");
            }

            // 验证密码
            if (!passwordEncoder.matches(request.getPassword(), user.getPassword())) {
                return Result.badRequest("用户名或密码错误");
            }

            // 生成JWT token
            String token = jwtUtil.generateToken(user.getUsername(), user.getRole().toString(), user.getId());

            // 构建响应
            LoginResponse response = new LoginResponse();
            response.setToken(token);
            response.setUserId(user.getId());
            response.setUsername(user.getUsername());
            response.setEmail(user.getEmail());
            response.setRole(user.getRole().toString());

            return Result.success("登录成功", response);
        } catch (Exception e) {
            return Result.error("登录失败：" + e.getMessage());
        }
    }

    /**
     * 用户注册
     */
    @PostMapping("/register")
    public Result<Map<String, Object>> register(@Valid @RequestBody RegisterRequest request) {
        try {
            // 检查用户名是否已存在
            if (userService.existsByUsername(request.getUsername())) {
                return Result.badRequest("用户名已存在");
            }

            // 检查邮箱是否已存在
            if (userService.existsByEmail(request.getEmail())) {
                return Result.badRequest("邮箱已存在");
            }

            // 创建新用户
            User user = new User();
            user.setUsername(request.getUsername());
            user.setEmail(request.getEmail());
            user.setPassword(passwordEncoder.encode(request.getPassword()));
            user.setRole(User.UserRole.USER); // 默认角色

            // 保存用户
            User savedUser = userService.save(user);

            // 构建响应
            Map<String, Object> userData = new HashMap<>();
            userData.put("id", savedUser.getId());
            userData.put("username", savedUser.getUsername());
            userData.put("email", savedUser.getEmail());
            userData.put("role", savedUser.getRole());

            return Result.success("注册成功", userData);
        } catch (Exception e) {
            return Result.error("注册失败：" + e.getMessage());
        }
    }

    /**
     * 获取当前用户信息
     */
    @GetMapping("/current")
    public Result<Map<String, Object>> getCurrentUser(@RequestHeader(value = "Authorization", required = false) String token) {
        try {
            if (token == null || !token.startsWith("Bearer ")) {
                return Result.unauthorized("未登录");
            }

            // 提取token
            String jwtToken = token.substring(7);

            // 获取用户名并验证token
            String username = jwtUtil.getUsernameFromToken(jwtToken);
            if (!jwtUtil.validateToken(jwtToken, username)) {
                return Result.unauthorized("token无效");
            }
            User user = userService.findByUsername(username);

            if (user == null) {
                return Result.unauthorized("用户不存在");
            }

            // 构建响应
            Map<String, Object> userData = new HashMap<>();
            userData.put("id", user.getId());
            userData.put("username", user.getUsername());
            userData.put("email", user.getEmail());
            userData.put("role", user.getRole());

            return Result.success(userData);
        } catch (Exception e) {
            return Result.unauthorized("获取用户信息失败：" + e.getMessage());
        }
    }
}
