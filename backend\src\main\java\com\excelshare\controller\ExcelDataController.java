package com.excelshare.controller;

import com.excelshare.common.Result;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.*;

/**
 * Excel数据控制器 - 模拟版本
 * 
 * <AUTHOR> Team
 */
@RestController
@RequestMapping("/api/excel-data")
@CrossOrigin(origins = "*")
public class ExcelDataController {

    // 模拟Excel数据存储
    private static final Map<Long, List<Map<String, Object>>> mockExcelData = new HashMap<>();
    
    static {
        // 初始化模拟数据
        List<Map<String, Object>> file1Data = new ArrayList<>();
        file1Data.add(Map.of(
            "id", 1L,
            "excelId", 1L,
            "userId", 1L,
            "username", "admin",
            "rowNumber", 1,
            "cellData", Map.of("A", "姓名", "B", "年龄", "C", "部门", "D", "职位"),
            "createdAt", LocalDateTime.now(),
            "updatedAt", LocalDateTime.now()
        ));
        file1Data.add(Map.of(
            "id", 2L,
            "excelId", 1L,
            "userId", 1L,
            "username", "admin",
            "rowNumber", 2,
            "cellData", Map.of("A", "张三", "B", "25", "C", "技术部", "D", "工程师"),
            "createdAt", LocalDateTime.now(),
            "updatedAt", LocalDateTime.now()
        ));
        file1Data.add(Map.of(
            "id", 3L,
            "excelId", 1L,
            "userId", 2L,
            "username", "user",
            "rowNumber", 3,
            "cellData", Map.of("A", "李四", "B", "28", "C", "销售部", "D", "销售经理"),
            "createdAt", LocalDateTime.now(),
            "updatedAt", LocalDateTime.now()
        ));
        
        mockExcelData.put(1L, file1Data);
        mockExcelData.put(2L, new ArrayList<>());
    }

    /**
     * 获取Excel文件数据
     */
    @GetMapping("/{excelId}")
    public Result<List<Map<String, Object>>> getExcelData(
            @PathVariable Long excelId,
            @RequestHeader(value = "Authorization", required = false) String token) {
        
        if (token == null || !token.startsWith("Bearer ")) {
            return Result.unauthorized("未登录");
        }
        
        List<Map<String, Object>> data = mockExcelData.getOrDefault(excelId, new ArrayList<>());
        
        // 如果是普通用户，只返回自己的数据
        if (!token.contains("admin")) {
            data = data.stream()
                    .filter(row -> "user".equals(row.get("username")))
                    .toList();
        }
        
        return Result.success(data);
    }

    /**
     * 保存Excel数据
     */
    @PostMapping("/save")
    public Result<Map<String, Object>> saveExcelData(
            @RequestBody Map<String, Object> request,
            @RequestHeader(value = "Authorization", required = false) String token) {
        
        if (token == null || !token.startsWith("Bearer ")) {
            return Result.unauthorized("未登录");
        }
        
        Long excelId = Long.valueOf(request.get("excelId").toString());
        Integer rowNumber = Integer.valueOf(request.get("rowNumber").toString());
        Map<String, Object> cellData = (Map<String, Object>) request.get("cellData");
        
        List<Map<String, Object>> data = mockExcelData.computeIfAbsent(excelId, k -> new ArrayList<>());
        
        // 查找现有行
        Optional<Map<String, Object>> existingRow = data.stream()
                .filter(row -> excelId.equals(row.get("excelId")) && rowNumber.equals(row.get("rowNumber")))
                .findFirst();
        
        Map<String, Object> rowData;
        if (existingRow.isPresent()) {
            // 更新现有行
            rowData = existingRow.get();
            rowData.put("cellData", cellData);
            rowData.put("updatedAt", LocalDateTime.now());
        } else {
            // 创建新行
            rowData = new HashMap<>();
            rowData.put("id", System.currentTimeMillis());
            rowData.put("excelId", excelId);
            rowData.put("userId", token.contains("admin") ? 1L : 2L);
            rowData.put("username", token.contains("admin") ? "admin" : "user");
            rowData.put("rowNumber", rowNumber);
            rowData.put("cellData", cellData);
            rowData.put("createdAt", LocalDateTime.now());
            rowData.put("updatedAt", LocalDateTime.now());
            data.add(rowData);
        }
        
        return Result.success("数据保存成功", rowData);
    }

    /**
     * 删除Excel数据行
     */
    @DeleteMapping("/{excelId}/row/{rowNumber}")
    public Result<String> deleteExcelDataRow(
            @PathVariable Long excelId,
            @PathVariable Integer rowNumber,
            @RequestHeader(value = "Authorization", required = false) String token) {
        
        if (token == null || !token.startsWith("Bearer ")) {
            return Result.unauthorized("未登录");
        }
        
        List<Map<String, Object>> data = mockExcelData.getOrDefault(excelId, new ArrayList<>());
        
        boolean removed = data.removeIf(row -> 
            excelId.equals(row.get("excelId")) && 
            rowNumber.equals(row.get("rowNumber")) &&
            (token.contains("admin") || "user".equals(row.get("username")))
        );
        
        if (removed) {
            return Result.success("数据删除成功");
        } else {
            return Result.badRequest("删除失败，数据不存在或无权限");
        }
    }

    /**
     * 添加新行
     */
    @PostMapping("/{excelId}/add-row")
    public Result<Map<String, Object>> addNewRow(
            @PathVariable Long excelId,
            @RequestBody Map<String, Object> cellData,
            @RequestHeader(value = "Authorization", required = false) String token) {
        
        if (token == null || !token.startsWith("Bearer ")) {
            return Result.unauthorized("未登录");
        }
        
        List<Map<String, Object>> data = mockExcelData.computeIfAbsent(excelId, k -> new ArrayList<>());
        
        // 获取下一个行号
        int maxRowNumber = data.stream()
                .mapToInt(row -> (Integer) row.get("rowNumber"))
                .max()
                .orElse(0);
        
        Map<String, Object> newRow = new HashMap<>();
        newRow.put("id", System.currentTimeMillis());
        newRow.put("excelId", excelId);
        newRow.put("userId", token.contains("admin") ? 1L : 2L);
        newRow.put("username", token.contains("admin") ? "admin" : "user");
        newRow.put("rowNumber", maxRowNumber + 1);
        newRow.put("cellData", cellData);
        newRow.put("createdAt", LocalDateTime.now());
        newRow.put("updatedAt", LocalDateTime.now());
        
        data.add(newRow);
        
        return Result.success("新行添加成功", newRow);
    }

    /**
     * 获取Excel文件列信息
     */
    @GetMapping("/{excelId}/columns")
    public Result<List<String>> getExcelColumns(
            @PathVariable Long excelId,
            @RequestHeader(value = "Authorization", required = false) String token) {
        
        if (token == null || !token.startsWith("Bearer ")) {
            return Result.unauthorized("未登录");
        }
        
        // 模拟列信息
        List<String> columns = Arrays.asList("A", "B", "C", "D", "E");
        return Result.success(columns);
    }

    /**
     * 从Excel文件导入数据 - 模拟版本
     */
    @PostMapping("/{excelId}/import")
    public Result<String> importDataFromExcel(
            @PathVariable Long excelId,
            @RequestHeader(value = "Authorization", required = false) String token) {
        
        if (token == null || !token.startsWith("Bearer ")) {
            return Result.unauthorized("未登录");
        }
        
        // 模拟导入成功
        return Result.success("数据导入成功");
    }
}
