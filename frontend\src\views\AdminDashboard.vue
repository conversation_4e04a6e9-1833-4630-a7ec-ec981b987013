<template>
  <div class="admin-dashboard-container">
    <h2>管理员仪表盘</h2>
    <p class="dashboard-description">系统概览和关键指标监控</p>

    <!-- 统计卡片 -->
    <div class="statistics-cards">
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-info">
            <p class="stat-label">总用户数</p>
            <el-statistic
              :value="stats.totalUsers"
              :precision="0"
              class="stat-value"
            ></el-statistic>
            <p class="stat-change">
              <span class="increase">+{{ stats.userGrowthRate }}%</span> 较上月
            </p>
          </div>
          <div class="stat-icon">
            <el-icon class="icon-large"><User /></el-icon>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-info">
            <p class="stat-label">Excel文件总数</p>
            <el-statistic
              :value="stats.totalFiles"
              :precision="0"
              class="stat-value"
            ></el-statistic>
            <p class="stat-change">
              <span class="increase">+{{ stats.fileGrowthRate }}%</span> 较上月
            </p>
          </div>
          <div class="stat-icon">
            <el-icon class="icon-large"><Document /></el-icon>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-info">
            <p class="stat-label">本月活跃用户</p>
            <el-statistic
              :value="stats.activeUsers"
              :precision="0"
              class="stat-value"
            ></el-statistic>
            <p class="stat-change">
              <span class="increase">+{{ stats.activeUserGrowthRate }}%</span> 较上月
            </p>
          </div>
          <div class="stat-icon">
            <el-icon class="icon-large"><UserFilled /></el-icon>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-info">
            <p class="stat-label">本月文件上传量</p>
            <el-statistic
              :value="stats.monthlyUploads"
              :precision="0"
              class="stat-value"
            ></el-statistic>
            <p class="stat-change">
              <span class="decrease">-{{ stats.uploadDeclineRate }}%</span> 较上月
            </p>
          </div>
          <div class="stat-icon">
            <el-icon class="icon-large"><UploadFilled /></el-icon>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 图表区域 -->
    <div class="charts-container">
      <el-card class="chart-card">
        <h3>用户增长趋势</h3>
        <div class="chart-wrapper">
          <el-empty description="图表加载中..." v-if="!chartDataLoaded"></el-empty>
          <div v-else class="chart-placeholder">
            <div class="chart-bar"></div>
            <div class="chart-bar"></div>
            <div class="chart-bar"></div>
            <div class="chart-bar"></div>
            <div class="chart-bar"></div>
            <div class="chart-bar"></div>
          </div>
        </div>
      </el-card>

      <el-card class="chart-card">
        <h3>文件类型分布</h3>
        <div class="chart-wrapper">
          <el-empty description="图表加载中..." v-if="!chartDataLoaded"></el-empty>
          <div v-else class="chart-pie-placeholder">
            <div class="chart-pie"></div>
            <div class="chart-legend">
              <div class="legend-item"><span class="color-dot primary"></span> 销售报表 (45%)</div>
              <div class="legend-item"><span class="color-dot success"></span> 库存数据 (30%)</div>
              <div class="legend-item"><span class="color-dot warning"></span> 财务数据 (15%)</div>
              <div class="legend-item"><span class="color-dot info"></span> 其他类型 (10%)</div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 最近活动 -->
    <el-card class="recent-activity-card">
      <h3>最近活动</h3>
      <el-table
        :data="recentActivities"
        size="small"
        :show-header="false"
        class="activity-table"
      >
        <el-table-column type="index" width="40"></el-table-column>
        <el-table-column prop="description" width="600"></el-table-column>
        <el-table-column prop="time" width="160"></el-table-column>
      </el-table>
    </el-card>

    <!-- 快速操作 -->
    <div class="quick-actions">
      <h3>快速操作</h3>
      <div class="action-buttons">
        <el-button type="primary" icon="User" size="large" @click="gotoUserManagement">
          用户管理
        </el-button>
        <el-button type="success" icon="Document" size="large" @click="gotoExcelList">
          文件管理
        </el-button>
        <el-button type="warning" icon="Setting" size="large" @click="gotoSystemSettings">
          系统设置
        </el-button>
        <el-button type="info" icon="HelpFilled" size="large" @click="gotoHelpCenter">
          帮助中心
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { User, Document, UserFilled, UploadFilled } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';

const router = useRouter();
const chartDataLoaded = ref(false);

// 统计数据（模拟）
const stats = ref({
  totalUsers: 128,
  totalFiles: 356,
  activeUsers: 87,
  monthlyUploads: 42,
  userGrowthRate: 12.5,
  fileGrowthRate: 8.3,
  activeUserGrowthRate: 15.2,
  uploadDeclineRate: 3.7
});

// 最近活动数据（模拟）
const recentActivities = ref([
  { description: '用户 <strong>user1</strong> 上传了新文件 <strong>Q3销售报表.xlsx</strong>', time: '今天 09:45' },
  { description: '管理员 <strong>admin</strong> 修改了文件 <strong>库存数据.xlsx</strong> 的权限设置', time: '昨天 16:20' },
  { description: '用户 <strong>user3</strong> 删除了文件 <strong>旧数据备份.xlsx</strong>', time: '昨天 14:10' },
  { description: '用户 <strong>user2</strong> 编辑了文件 <strong>财务报表.xlsx</strong>', time: '2023-11-28 11:35' },
  { description: '系统自动备份了所有数据', time: '2023-11-27 23:00' },
  { description: '管理员 <strong>admin</strong> 添加了新用户 <strong>user4</strong>', time: '2023-11-26 15:40' }
]);

// 页面加载时模拟数据加载
onMounted(() => {
  // 模拟图表数据加载延迟
  setTimeout(() => {
    chartDataLoaded.value = true;
  }, 800);
});

// 导航到用户管理
const gotoUserManagement = () => {
  router.push('/user-management');
};

// 导航到文件列表
const gotoExcelList = () => {
  router.push('/excel');
};

// 导航到系统设置（预留功能）
const gotoSystemSettings = () => {
  ElMessage.info('系统设置功能即将上线');
};

// 导航到帮助中心（预留功能）
const gotoHelpCenter = () => {
  ElMessage.info('帮助中心功能即将上线');
};
</script>

<style scoped>
.admin-dashboard-container {
  padding: 20px;
}

.dashboard-description {
  color: #606266;
  margin-bottom: 25px;
  font-size: 14px;
}

.statistics-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  height: 100%;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  color: #606266;
  font-size: 14px;
  margin-bottom: 5px;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
}

.stat-change {
  font-size: 12px;
  margin-top: 5px;
}

.increase {
  color: #f56c6c;
}

.decrease {
  color: #409eff;
}

.stat-icon {
  font-size: 36px;
  color: #409eff;
}

.icon-large {
  font-size: 40px;
}

.charts-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.chart-card {
  height: 100%;
}

.chart-wrapper {
  height: 250px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  width: 100%;
  height: 200px;
  display: flex;
  align-items: flex-end;
  justify-content: space-around;
  padding-bottom: 20px;
}

.chart-bar {
  width: 40px;
  background-color: #409eff;
  border-radius: 4px 4px 0 0;
  margin: 0 5px;
  animation: barGrow 0.8s ease-out;
}

.chart-bar:nth-child(1) { height: 120px; }
.chart-bar:nth-child(2) { height: 150px; background-color: #67c23a; }
.chart-bar:nth-child(3) { height: 90px; background-color: #e6a23c; }
.chart-bar:nth-child(4) { height: 180px; background-color: #f56c6c; }
.chart-bar:nth-child(5) { height: 140px; background-color: #909399; }
.chart-bar:nth-child(6) { height: 160px; background-color: #8c8c8c; }

.chart-pie-placeholder {
  width: 100%;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.chart-pie {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  background: conic-gradient(
    #409eff 0% 45%,
    #67c23a 45% 75%,
    #e6a23c 75% 90%,
    #909399 90% 100%
  );
  position: relative;
}

.chart-pie::after {
  content: '';
  position: absolute;
  width: 70px;
  height: 70px;
  background-color: white;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.chart-legend {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.legend-item {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.color-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
}

.color-dot.primary { background-color: #409eff; }
.color-dot.success { background-color: #67c23a; }
.color-dot.warning { background-color: #e6a23c; }
.color-dot.info { background-color: #909399; }

.recent-activity-card {
  margin-bottom: 30px;
}

.activity-table .cell {
  padding: 12px 0;
}

.quick-actions {
  margin-bottom: 30px;
}

.action-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.action-buttons .el-button--large {
  height: 80px;
  font-size: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.action-buttons .el-icon {
  font-size: 24px;
  margin-bottom: 5px;
}

@keyframes barGrow {
  from { height: 0; }
  to { height: var(--height); }
}

/* 响应式调整 */
@media (max-width: 1024px) {
  .charts-container {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .statistics-cards {
    grid-template-columns: 1fr 1fr;
  }

  .chart-placeholder {
    height: 150px;
  }

  .chart-bar {
    width: 30px;
  }
}

@media (max-width: 480px) {
  .statistics-cards {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    grid-template-columns: 1fr;
  }
}
</style>