package com.excelshare.entity;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Excel数据实体类
 * 
 * <AUTHOR> Team
 */
public class ExcelData {
    
    private Long id;
    private Long excelId;
    private Long userId;
    private Integer rowNumber;
    private Map<String, Object> cellData;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    // 构造函数
    public ExcelData() {}

    public ExcelData(Long excelId, Long userId, Integer rowNumber, Map<String, Object> cellData) {
        this.excelId = excelId;
        this.userId = userId;
        this.rowNumber = rowNumber;
        this.cellData = cellData;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getExcelId() {
        return excelId;
    }

    public void setExcelId(Long excelId) {
        this.excelId = excelId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getRowNumber() {
        return rowNumber;
    }

    public void setRowNumber(Integer rowNumber) {
        this.rowNumber = rowNumber;
    }

    public Map<String, Object> getCellData() {
        return cellData;
    }

    public void setCellData(Map<String, Object> cellData) {
        this.cellData = cellData;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public String toString() {
        return "ExcelData{" +
                "id=" + id +
                ", excelId=" + excelId +
                ", userId=" + userId +
                ", rowNumber=" + rowNumber +
                ", cellData=" + cellData +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}
