import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import axios from 'axios';

// 创建axios实例
const api = axios.create({
  baseURL: 'http://localhost:8080/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

export const useUserStore = defineStore('user', () => {
  // 状态
  const currentUser = ref(JSON.parse(localStorage.getItem('currentUser')) || null);
  const token = ref(localStorage.getItem('token') || null);
  const isLoggedIn = computed(() => !!currentUser.value && !!token.value);

  // 操作
  const login = async (credentials) => {
    try {
      const response = await api.post('/auth/login', credentials);

      if (response.data.code === 200 && response.data.data) {
        const { user: userData, token: tokenValue } = response.data.data;

        currentUser.value = userData;
        token.value = tokenValue;
        localStorage.setItem('currentUser', JSON.stringify(userData));
        localStorage.setItem('token', tokenValue);

        return { success: true, user: userData, token: tokenValue };
      } else {
        throw new Error(response.data.message || '登录失败');
      }
    } catch (error) {
      console.error('Login error:', error);
      if (error.response && error.response.data) {
        throw new Error(error.response.data.message || '登录失败');
      }
      throw error;
    }
  };

  const register = async (userData) => {
    try {
      const response = await api.post('/auth/register', userData);

      if (response.data.code === 200) {
        return { success: true, message: '注册成功', data: response.data.data };
      } else {
        return { success: false, message: response.data.message || '注册失败' };
      }
    } catch (error) {
      console.error('Register error:', error);
      if (error.response && error.response.data) {
        return { success: false, message: error.response.data.message || '注册失败' };
      }
      return { success: false, message: '网络错误' };
    }
  };

  const getCurrentUser = async () => {
    try {
      const response = await api.get('/auth/current', {
        headers: {
          'Authorization': `Bearer ${token.value}`
        }
      });

      if (response.data.code === 200 && response.data.data) {
        currentUser.value = response.data.data;
        localStorage.setItem('currentUser', JSON.stringify(response.data.data));
        return response.data.data;
      } else {
        throw new Error(response.data.message || '获取用户信息失败');
      }
    } catch (error) {
      console.error('Get current user error:', error);
      // 如果获取用户信息失败，清除本地存储
      logout();
      throw error;
    }
  };

  const logout = () => {
    currentUser.value = null;
    token.value = null;
    localStorage.removeItem('currentUser');
    localStorage.removeItem('token');
  };

  const initUser = () => {
    const savedUser = localStorage.getItem('currentUser');
    const savedToken = localStorage.getItem('token');
    if (savedUser && savedToken) {
      currentUser.value = JSON.parse(savedUser);
      token.value = savedToken;
    }
  };

  // 获取所有用户（仅管理员）
  const getAllUsers = () => {
    if (currentUser.value?.role === 'ADMIN') {
      return [...mockUsers];
    }
    return [];
  };

  // 删除用户（仅管理员）
  const deleteUser = (userId) => {
    if (currentUser.value?.role !== 'ADMIN') return false;
    if (userId === currentUser.value.id) return false; // 不能删除自己

    const index = mockUsers.findIndex(u => u.id === userId);
    if (index !== -1) {
      mockUsers.splice(index, 1);
      return true;
    }
    return false;
  };

  return {
    currentUser,
    token,
    isLoggedIn,
    login,
    register,
    getCurrentUser,
    logout,
    initUser,
    getAllUsers,
    deleteUser
  };
});