package com.excelshare.mapper;

import com.excelshare.entity.ExcelData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * Excel数据Mapper接口
 *
 * <AUTHOR> Team
 */
@Mapper
public interface UserExcelDataMapper {

    /**
     * 根据Excel文件ID和用户ID查询数据
     */
    @Select("SELECT ued.*, u.username, ef.file_name " +
            "FROM excel_data ued " +
            "LEFT JOIN users u ON ued.user_id = u.id " +
            "LEFT JOIN excel_files ef ON ued.excel_id = ef.id " +
            "WHERE ued.excel_id = #{excelId} AND ued.user_id = #{userId} " +
            "ORDER BY ued.`row_number` ASC")
    List<ExcelData> findByExcelIdAndUserId(Long excelId, Long userId);

    /**
     * 根据Excel文件ID查询所有用户数据（管理员用）
     */
    @Select("SELECT ued.*, u.username, ef.file_name " +
            "FROM excel_data ued " +
            "LEFT JOIN users u ON ued.user_id = u.id " +
            "LEFT JOIN excel_files ef ON ued.excel_id = ef.id " +
            "WHERE ued.excel_id = #{excelId} " +
            "ORDER BY ued.user_id ASC, ued.`row_number` ASC")
    List<ExcelData> findByExcelIdWithUserInfo(Long excelId);

    /**
     * 删除指定Excel文件的所有用户数据
     */
    @Delete("DELETE FROM excel_data WHERE excel_id = #{excelId}")
    void deleteByExcelId(Long excelId);

    /**
     * 删除指定用户在指定Excel文件中的所有数据
     */
    @Delete("DELETE FROM excel_data WHERE excel_id = #{excelId} AND user_id = #{userId}")
    void deleteByExcelIdAndUserId(Long excelId, Long userId);

    /**
     * 查询指定Excel文件的最大行号
     */
    @Select("SELECT COALESCE(MAX(`row_number`), 0) FROM excel_data WHERE excel_id = #{excelId} AND user_id = #{userId}")
    Integer getMaxRowNumber(Long excelId, Long userId);
}
