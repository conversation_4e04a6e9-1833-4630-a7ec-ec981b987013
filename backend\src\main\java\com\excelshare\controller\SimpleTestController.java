package com.excelshare.controller;

import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 简单测试控制器
 * 用于验证基础功能
 * 
 * <AUTHOR> Team
 */
@RestController
@RequestMapping("/api/simple")
@CrossOrigin(origins = "*")
public class SimpleTestController {

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Map<String, Object> health() {
        Map<String, Object> data = new HashMap<>();
        data.put("status", "UP");
        data.put("timestamp", LocalDateTime.now());
        data.put("service", "Excel共享平台");
        data.put("version", "1.0.0");
        data.put("message", "服务正常运行");
        return data;
    }

    /**
     * 测试CORS
     */
    @GetMapping("/cors")
    public Map<String, Object> testCors() {
        Map<String, Object> data = new HashMap<>();
        data.put("message", "CORS配置正常");
        data.put("timestamp", LocalDateTime.now());
        return data;
    }

    /**
     * 测试POST请求
     */
    @PostMapping("/echo")
    public Map<String, Object> echo(@RequestBody Map<String, Object> data) {
        Map<String, Object> response = new HashMap<>();
        response.put("received", data);
        response.put("timestamp", LocalDateTime.now());
        response.put("message", "数据接收成功");
        return response;
    }
}
