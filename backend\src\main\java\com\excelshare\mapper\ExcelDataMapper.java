package com.excelshare.mapper;

import com.excelshare.entity.ExcelData;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * Excel数据访问层
 * 
 * <AUTHOR> Team
 */
@Mapper
public interface ExcelDataMapper {

    /**
     * 根据ID查询Excel数据
     */
    @Select("SELECT * FROM excel_data WHERE id = #{id}")
    ExcelData findById(@Param("id") Long id);

    /**
     * 根据Excel文件ID查询所有数据
     */
    @Select("SELECT * FROM excel_data WHERE excel_id = #{excelId} ORDER BY row_number ASC")
    List<ExcelData> findByExcelId(@Param("excelId") Long excelId);

    /**
     * 根据Excel文件ID和用户ID查询数据
     */
    @Select("SELECT * FROM excel_data WHERE excel_id = #{excelId} AND user_id = #{userId} ORDER BY row_number ASC")
    List<ExcelData> findByExcelIdAndUserId(@Param("excelId") Long excelId, @Param("userId") Long userId);

    /**
     * 根据Excel文件ID和行号查询数据
     */
    @Select("SELECT * FROM excel_data WHERE excel_id = #{excelId} AND row_number = #{rowNumber}")
    ExcelData findByExcelIdAndRowNumber(@Param("excelId") Long excelId, @Param("rowNumber") Integer rowNumber);

    /**
     * 获取Excel文件的最大行号
     */
    @Select("SELECT COALESCE(MAX(row_number), 0) FROM excel_data WHERE excel_id = #{excelId}")
    int getMaxRowNumber(@Param("excelId") Long excelId);

    /**
     * 插入新的Excel数据
     */
    @Insert("INSERT INTO excel_data (excel_id, user_id, row_number, cell_data, created_at, updated_at) " +
            "VALUES (#{excelId}, #{userId}, #{rowNumber}, #{cellData, typeHandler=com.excelshare.config.JsonTypeHandler}, #{createdAt}, #{updatedAt})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(ExcelData excelData);

    /**
     * 更新Excel数据
     */
    @Update("UPDATE excel_data SET cell_data = #{cellData, typeHandler=com.excelshare.config.JsonTypeHandler}, " +
            "updated_at = #{updatedAt} WHERE id = #{id}")
    int update(ExcelData excelData);

    /**
     * 根据Excel文件ID和行号删除数据
     */
    @Delete("DELETE FROM excel_data WHERE excel_id = #{excelId} AND row_number = #{rowNumber}")
    int deleteByExcelIdAndRowNumber(@Param("excelId") Long excelId, @Param("rowNumber") Integer rowNumber);

    /**
     * 根据Excel文件ID和用户ID删除数据
     */
    @Delete("DELETE FROM excel_data WHERE excel_id = #{excelId} AND user_id = #{userId}")
    int deleteByExcelIdAndUserId(@Param("excelId") Long excelId, @Param("userId") Long userId);

    /**
     * 根据Excel文件ID删除所有数据
     */
    @Delete("DELETE FROM excel_data WHERE excel_id = #{excelId}")
    int deleteByExcelId(@Param("excelId") Long excelId);

    /**
     * 统计Excel文件的数据行数
     */
    @Select("SELECT COUNT(*) FROM excel_data WHERE excel_id = #{excelId}")
    int countByExcelId(@Param("excelId") Long excelId);
}
