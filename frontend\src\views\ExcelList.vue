<template>
  <div class="excel-list-container">
    <div class="header-actions">
      <h2>Excel文件列表</h2>
      <el-upload
        class="upload-btn"
        action="#"
        :auto-upload="false"
        :show-file-list="false"
        @click="handleUploadClick"
      >
        <el-button type="primary" icon="Upload">上传Excel文件</el-button>
      </el-upload>
    </div>

    <el-card shadow="hover" class="excel-table-card">
      <el-table
        :data="excelFiles"
        border
        stripe
        :size="isMobile ? 'default' : 'default'"
        class="responsive-table"
        :class="{ 'mobile-table': isMobile }"
      >
        <el-table-column
          type="index"
          label="序号"
          :width="isMobile ? 50 : 80"
          :fixed="isMobile ? false : 'left'"
        ></el-table-column>
        <el-table-column
          prop="fileName"
          label="文件名"
          :min-width="isMobile ? 120 : 200"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          v-if="!isMobile"
          prop="description"
          label="描述"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          v-if="!isMobile"
          prop="createdAt"
          label="创建时间"
          width="180"
        ></el-table-column>
        <el-table-column
          v-if="!isMobile"
          prop="createdBy"
          label="创建者"
          width="120"
        ></el-table-column>
        <el-table-column
          label="操作"
          :width="isMobile ? 100 : 180"
          :fixed="isMobile ? false : 'right'"
        >
          <template #default="{ row }">
            <div class="action-buttons" :class="{ 'mobile-actions': isMobile }">
              <el-button
                type="primary"
                :size="isMobile ? 'small' : 'small'"
                :icon="isMobile ? 'Edit' : undefined"
                @click="handleEdit(row.id)"
                class="action-btn"
              >
                <span v-if="!isMobile">编辑</span>
              </el-button>
              <el-button
                v-if="isAdmin"
                type="danger"
                :size="isMobile ? 'small' : 'small'"
                :icon="isMobile ? 'Delete' : undefined"
                @click="handleDelete(row.id)"
                class="action-btn"
              >
                <span v-if="!isMobile">删除</span>
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[5, 10, 20]"
          :total="excelFiles.length"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </el-card>

    <!-- 上传文件对话框 -->
    <el-dialog v-model="uploadDialogVisible" title="上传Excel文件" width="500px">
      <el-upload
        ref="upload"
        class="upload-excel"
        action="#"
        :on-change="handleFileChange"
        :auto-upload="false"
        :show-file-list="true"
        accept=".xlsx,.xls"
      >
        <el-button type="primary">选择Excel文件</el-button>
        <div class="upload-tip">支持 .xlsx 和 .xls 格式，文件大小不超过10MB</div>
      </el-upload>
      <el-form :model="uploadForm" :rules="uploadRules" ref="uploadFormRef">
        <el-form-item label="文件名称" prop="fileName">
          <el-input v-model="uploadForm.fileName" placeholder="请输入文件名称"></el-input>
        </el-form-item>
        <el-form-item label="文件描述" prop="description">
          <el-input
            v-model="uploadForm.description"
            type="textarea"
            rows="3"
            placeholder="请输入文件描述"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="uploadDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleFileUpload">上传</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { useUserStore } from '../stores/user';
import { ElMessage, ElMessageBox } from 'element-plus';

const router = useRouter();
const userStore = useUserStore();
const uploadDialogVisible = ref(false);
const currentPage = ref(1);
const pageSize = ref(10);
const selectedFile = ref(null);
const isMobile = ref(false);

// 模拟Excel文件数据
const excelFiles = ref([
  {
    id: 1,
    fileName: '销售数据报表.xlsx',
    description: '2023年度销售数据汇总表',
    createdAt: '2023-10-15',
    createdBy: 'admin'
  },
  {
    id: 2,
    fileName: '员工信息表.xlsx',
    description: '公司员工基本信息登记',
    createdAt: '2023-10-10',
    createdBy: 'admin'
  },
  {
    id: 3,
    fileName: '项目进度跟踪.xlsx',
    description: '各部门项目进度跟踪表',
    createdAt: '2023-10-05',
    createdBy: 'admin'
  },
  {
    id: 4,
    fileName: '财务支出记录.xlsx',
    description: '日常办公费用支出明细',
    createdAt: '2023-09-28',
    createdBy: 'admin'
  },
  {
    id: 5,
    fileName: '客户反馈收集.xlsx',
    description: '产品用户反馈信息收集',
    createdAt: '2023-09-20',
    createdBy: 'admin'
  }
]);

// 上传表单数据
const uploadForm = reactive({
  fileName: '',
  description: ''
});

// 上传表单验证规则
const uploadRules = {
  fileName: [
    { required: true, message: '请输入文件名称', trigger: 'blur' },
    { max: 50, message: '文件名称不能超过50个字符', trigger: 'blur' }
  ],
  description: [
    { max: 200, message: '文件描述不能超过200个字符', trigger: 'blur' }
  ]
};

// 计算属性 - 是否为管理员
const isAdmin = computed(() => userStore.currentUser?.role === 'ADMIN');

// 处理文件上传对话框显示
const handleUploadClick = () => {
  uploadDialogVisible.value = true;
  uploadForm.fileName = '';
  uploadForm.description = '';
  selectedFile.value = null;
};

// 处理文件选择
const handleFileChange = (file) => {
  selectedFile.value = file;
  // 自动填充文件名（不含扩展名）
  if (!uploadForm.fileName && file.name) {
    uploadForm.fileName = file.name.replace(/\.(xlsx|xls)$/, '');
  }
};

// 处理文件上传（模拟）
const handleFileUpload = () => {
  if (!selectedFile.value) {
    ElMessage.error('请选择要上传的Excel文件');
    return;
  }

  // 模拟上传成功
  ElMessage.success('文件上传成功');
  // 添加到文件列表
  excelFiles.value.unshift({
    id: excelFiles.value.length + 1,
    fileName: uploadForm.fileName + '.' + selectedFile.value.name.split('.').pop(),
    description: uploadForm.description,
    createdAt: new Date().toLocaleDateString(),
    createdBy: userStore.currentUser.username
  });
  uploadDialogVisible.value = false;
};

// 编辑Excel文件
const handleEdit = (id) => {
  router.push(`/excel/${id}`);
};

// 删除Excel文件
const handleDelete = (id) => {
  ElMessageBox.confirm('确定要删除此文件吗？此操作不可恢复。', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      // 从列表中移除
      const index = excelFiles.value.findIndex(file => file.id === id);
      if (index !== -1) {
        excelFiles.value.splice(index, 1);
        ElMessage.success('文件已删除');
      }
    })
    .catch(() => {
      // 取消删除
    });
};

// 分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1;
};

// 当前页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
};

// 响应式处理
const handleResize = () => {
  isMobile.value = window.innerWidth < 768;
};

// 生命周期
onMounted(() => {
  handleResize();
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});
</script>

<style scoped>
.excel-list-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.header-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.upload-btn {
  margin-bottom: 10px;
}

.excel-table-card {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.el-table {
  flex: 1;
  min-height: 300px;
}

.pagination-container {
  margin-top: 15px;
  text-align: right;
}

.upload-excel {
  margin-bottom: 20px;
}

.upload-tip {
  color: #606266;
  font-size: 12px;
  margin-top: 5px;
}

.responsive-table {
  width: 100%;
}

.mobile-table {
  font-size: 14px;
}

.mobile-table .el-table__cell {
  padding: 10px 8px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.mobile-actions {
  flex-direction: column;
  gap: 4px;
}

.mobile-actions .action-btn {
  min-width: 32px;
  padding: 4px 8px;
}

/* 响应式样式 */
@media (max-width: 1200px) {
  .header-actions {
    flex-wrap: wrap;
    gap: 10px;
  }
}

@media (max-width: 768px) {
  .excel-list-container {
    padding: 0;
  }

  .header-actions {
    flex-direction: column;
    align-items: stretch;
    padding: 15px;
    gap: 15px;
  }

  .header-actions h2 {
    font-size: 18px;
    text-align: center;
    margin: 0;
  }

  .upload-btn {
    align-self: center;
    margin: 0;
  }

  .excel-table-card {
    margin: 0 10px;
    border-radius: 0;
  }

  .pagination-container {
    text-align: center;
    margin-top: 10px;
  }

  /* 分页组件在移动端的调整 */
  .el-pagination {
    justify-content: center;
  }

  .el-pagination .el-pager {
    flex-wrap: wrap;
  }

  /* 上传对话框在移动端的调整 */
  .el-dialog {
    width: 95% !important;
    margin: 0 auto;
  }
}

@media (max-width: 480px) {
  .header-actions {
    padding: 10px;
  }

  .header-actions h2 {
    font-size: 16px;
  }

  .excel-table-card {
    margin: 0 8px;
  }

  .mobile-table .el-table__cell {
    padding: 8px 4px;
    font-size: 12px;
  }

  .action-buttons {
    gap: 4px;
  }

  .mobile-actions .action-btn {
    font-size: 12px;
    min-width: 28px;
    padding: 2px 6px;
  }

  /* 分页在小屏幕上的优化 */
  .el-pagination .el-pagination__sizes {
    display: none;
  }

  .el-pagination .el-pagination__jump {
    display: none;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .el-table .el-table__cell {
    padding: 12px 8px;
  }

  .action-btn {
    min-height: 44px;
    min-width: 44px;
  }
}
</style>