<template>
  <div class="not-found-container">
    <div class="error-code">404</div>
    <h2>页面未找到</h2>
    <p>抱歉，您访问的页面不存在或已被移除</p>
    <el-button
      type="primary"
      icon="ArrowLeft"
      @click="handleGoBack"
      class="back-button"
    >
      返回上一页
    </el-button>
    <el-button
      icon="Home"
      @click="handleGoHome"
      class="home-button"
    >
      返回首页
    </el-button>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';

const router = useRouter();

const handleGoBack = () => {
  if (window.history.length > 1) {
    router.go(-1);
  } else {
    router.push('/');
  }
};

const handleGoHome = () => {
  router.push('/');
};
</script>

<style scoped>
.not-found-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 60px);
  text-align: center;
  padding: 20px;
}

.error-code {
  font-size: 120px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 20px;
  position: relative;
  display: inline-block;
}

.error-code::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 150%;
  height: 150%;
  background-color: rgba(64, 158, 255, 0.1);
  border-radius: 50%;
  z-index: -1;
}

.not-found-container h2 {
  font-size: 24px;
  margin-bottom: 10px;
  color: #303133;
}

.not-found-container p {
  color: #606266;
  margin-bottom: 30px;
  max-width: 400px;
}

.back-button {
  margin-right: 10px;
}

.home-button {
  margin-left: 10px;
}
</style>