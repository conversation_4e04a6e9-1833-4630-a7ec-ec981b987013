<template>
  <el-container class="app-container">
    <!-- 移动端遮罩层 -->
    <div
      v-if="isMobile && !isCollapse"
      class="mobile-overlay"
      @click="isCollapse = true"
    ></div>

    <!-- 侧边栏 -->
    <el-aside
      :width="isCollapse ? '64px' : '200px'"
      class="sidebar"
      :class="{
        'collapsed': isCollapse,
        'mobile': isMobile,
        'mobile-open': isMobile && !isCollapse
      }"
    >
      <div class="logo-container">
        <img src="/vite.svg" alt="Logo" class="logo" />
        <h1 v-if="!isCollapse">Excel共享平台</h1>
      </div>
      <el-menu
        active-text-color="#1890ff"
        background-color="#001529"
        text-color="#fff"
        :collapse="isCollapse"
        :collapse-transition="false"
        router
      >
        <!-- 管理员菜单 -->
        <template v-if="userRole === 'ADMIN'">
          <el-menu-item index="/admin/dashboard" @click="handleMenuClick">
            <el-icon><HomeFilled /></el-icon>
            <span v-if="!isCollapse" slot="title">控制台</span>
          </el-menu-item>
          <el-menu-item index="/admin/users" @click="handleMenuClick">
            <el-icon><User /></el-icon>
            <span v-if="!isCollapse" slot="title">用户管理</span>
          </el-menu-item>
        </template>

        <!-- 公共菜单 -->
        <el-menu-item index="/excel" @click="handleMenuClick">
          <el-icon><FolderOpened /></el-icon>
          <span v-if="!isCollapse" slot="title">Excel文件</span>
        </el-menu-item>
      </el-menu>
      <el-button
        class="collapse-btn"
        @click="toggleSidebar"
        v-if="!isMobile"
      >
        <el-icon><Menu /></el-icon>
      </el-button>
    </el-aside>

    <!-- 主内容区 -->
    <el-container>
      <!-- 顶部导航 -->
      <el-header class="header">
        <!-- 移动端菜单按钮 -->
        <el-button
          v-if="isMobile"
          class="mobile-menu-btn"
          @click="toggleSidebar"
          text
        >
          <el-icon><Menu /></el-icon>
        </el-button>

        <div class="header-title" v-if="isMobile">
          <span>Excel共享平台</span>
        </div>

        <div class="user-info">
          <el-dropdown trigger="click">
            <span class="user-name">
              <el-icon><User /></el-icon>
              <span v-if="!isMobile">{{ userName }}</span>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item disabled>{{ userEmail }}</el-dropdown-item>
              <el-dropdown-item disabled>角色: {{ userRole === 'ADMIN' ? '管理员' : '普通用户' }}</el-dropdown-item>
              <el-dropdown-item divided @click="handleLogout">
                <el-icon><SwitchButton /></el-icon> 退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </el-header>

      <!-- 页面内容 -->
      <el-main class="main-content">
        <router-view />
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useUserStore } from '@/stores/user';
import { ElMessage } from 'element-plus';
import { HomeFilled, User, FolderOpened, SwitchButton, Menu } from '@element-plus/icons-vue';

const router = useRouter();
const userStore = useUserStore();
const isCollapse = ref(false);
const isMobile = ref(false);

// 响应式布局处理
const handleResize = () => {
  const newIsMobile = window.innerWidth < 768;
  if (newIsMobile !== isMobile.value) {
    isMobile.value = newIsMobile;
    if (isMobile.value) {
      isCollapse.value = true;
    }
  }
};

// 切换侧边栏
const toggleSidebar = () => {
  isCollapse.value = !isCollapse.value;
};

// 移动端菜单点击处理
const handleMenuClick = () => {
  if (isMobile.value) {
    isCollapse.value = true;
  }
};

onMounted(() => {
  window.addEventListener('resize', handleResize);
  handleResize(); // 初始化

  // 检查登录状态
  if (!userStore.isLoggedIn) {
    router.push('/login');
  }
});

// 计算属性获取用户信息
const userName = computed(() => userStore.currentUser?.username || '');
const userEmail = computed(() => userStore.currentUser?.email || '');
const userRole = computed(() => userStore.currentUser?.role || 'USER');

// 退出登录
const handleLogout = () => {
  userStore.logout();
  ElMessage.success('退出登录成功');
  router.push('/login');
};
</script>

<style scoped>
.app-container {
  height: 100vh;
  overflow: hidden;
  position: relative;
}

.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

.sidebar {
  background-color: #001529;
  color: #fff;
  transition: all 0.3s ease;
  height: 100vh;
  position: relative;
  z-index: 1000;
}

.sidebar.mobile {
  position: fixed;
  left: -200px;
  top: 0;
  width: 200px !important;
}

.sidebar.mobile.mobile-open {
  left: 0;
}

.sidebar.mobile.collapsed {
  left: -200px;
}

.logo-container {
  display: flex;
  align-items: center;
  padding: 0 15px;
  height: 60px;
  border-bottom: 1px solid #002140;
}

.logo {
  width: 32px;
  height: 32px;
  flex-shrink: 0;
}

.logo-container h1 {
  margin-left: 10px;
  font-size: 18px;
  color: #fff;
  white-space: nowrap;
  overflow: hidden;
}

.el-menu {
  border-right: none;
  height: calc(100vh - 60px);
  overflow-y: auto;
}

.collapse-btn {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #002140;
  color: #fff;
  border: none;
  min-height: 32px;
}

.header {
  background-color: #fff;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  height: 60px;
}

.mobile-menu-btn {
  color: #303133;
  font-size: 18px;
  padding: 8px;
}

.header-title {
  flex: 1;
  text-align: center;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.user-name {
  display: flex;
  align-items: center;
  color: #303133;
  padding: 8px;
}

.user-name .el-icon {
  margin-right: 5px;
}

.main-content {
  padding: 20px;
  overflow-y: auto;
  height: calc(100vh - 60px);
  background-color: #f5f7fa;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .main-content {
    padding: 15px 10px;
    height: calc(100vh - 60px);
  }

  .header {
    padding: 0 15px;
  }

  .user-name span {
    display: none;
  }

  .logo-container {
    padding: 0 10px;
  }

  .logo-container h1 {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: 10px 8px;
  }

  .header {
    padding: 0 10px;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .el-menu-item {
    min-height: 48px;
  }

  .collapse-btn {
    min-height: 44px;
    min-width: 44px;
  }

  .mobile-menu-btn {
    min-height: 44px;
    min-width: 44px;
  }
}
</style>
