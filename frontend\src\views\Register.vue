<template>
  <div class="register-container">
    <el-card class="register-card" shadow="hover">
      <div class="register-header">
        <h2>账户注册</h2>
        <p>创建新账户以使用Excel共享平台</p>
      </div>
      <el-form
        ref="registerFormRef"
        :model="registerForm"
        :rules="registerRules"
        status-icon
        label-width="100px"
        class="register-form"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="registerForm.username" placeholder="请输入用户名"></el-input>
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="registerForm.email" type="email" placeholder="请输入邮箱"></el-input>
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input
            v-model="registerForm.password"
            type="password"
            placeholder="请输入密码（至少6位）"
          ></el-input>
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="registerForm.confirmPassword"
            type="password"
            placeholder="请再次输入密码"
          ></el-input>
        </el-form-item>
        <el-form-item class="form-actions">
          <el-button type="primary" @click="handleRegister" :loading="loading">注册</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
        <div class="login-link">
          <span>已有账户？</span>
          <el-link type="primary" @click="goToLogin">立即登录</el-link>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { useUserStore } from '../stores/user';
import { ElMessage } from 'element-plus';

const router = useRouter();
const userStore = useUserStore();
const loading = ref(false);
const registerForm = reactive({
  username: '',
  email: '',
  password: '',
  confirmPassword: ''
});

// 密码验证函数
const validatePassword = (rule, value, callback) => {
  if (value.length < 6) {
    callback(new Error('密码长度不能少于6个字符'));
  } else if (!/[A-Za-z]/.test(value) || !/[0-9]/.test(value)) {
    callback(new Error('密码必须包含字母和数字'));
  } else {
    if (registerForm.confirmPassword) {
      // 触发确认密码验证
      registerFormRef.value.validateField('confirmPassword');
    }
    callback();
  }
};

// 确认密码验证
const validateConfirmPassword = (rule, value, callback) => {
  if (value !== registerForm.password) {
    callback(new Error('两次输入的密码不一致'));
  } else {
    callback();
  }
};

const registerRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { validator: validatePassword, trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' }
  ]
};

const registerFormRef = ref(null);

const resetForm = () => {
  registerFormRef.value.resetFields();
};

const handleRegister = async () => {
  try {
    await registerFormRef.value.validate();
    loading.value = true;

    const result = await userStore.register({
      username: registerForm.username,
      email: registerForm.email,
      password: registerForm.password
    });

    if (result.success) {
      ElMessage.success(result.message);
      router.push('/login');
    } else {
      ElMessage.error(result.message);
    }
  } catch (error) {
    ElMessage.error('请填写正确的注册信息');
  } finally {
    loading.value = false;
  }
};

const goToLogin = () => {
  router.push('/login');
};
</script>

<style scoped>
.register-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 20px;
  box-sizing: border-box;
}

.register-card {
  width: 100%;
  max-width: 450px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.register-header {
  text-align: center;
  margin-bottom: 25px;
  padding: 20px 25px 0;
}

.register-header h2 {
  color: #1890ff;
  margin-bottom: 5px;
  font-size: 24px;
}

.register-header p {
  color: #606266;
  margin: 0;
  font-size: 14px;
}

.register-form {
  padding: 0 25px 25px;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  gap: 10px;
}

.form-actions .el-button {
  flex: 1;
}

.login-link {
  margin-top: 15px;
  text-align: center;
  font-size: 14px;
}

.login-link span {
  color: #606266;
}

/* 响应式调整 */
@media (max-width: 480px) {
  .register-container {
    padding: 15px;
  }

  .register-card {
    border-radius: 8px;
  }

  .register-header {
    padding: 15px 20px 0;
    margin-bottom: 20px;
  }

  .register-header h2 {
    font-size: 20px;
  }

  .register-form {
    padding: 0 20px 20px;
  }

  .form-actions {
    flex-direction: column;
    gap: 8px;
  }

  .form-actions .el-button {
    width: 100%;
  }
}

@media (max-width: 360px) {
  .register-container {
    padding: 10px;
  }

  .register-header {
    padding: 10px 15px 0;
  }

  .register-form {
    padding: 0 15px 15px;
  }
}
</style>