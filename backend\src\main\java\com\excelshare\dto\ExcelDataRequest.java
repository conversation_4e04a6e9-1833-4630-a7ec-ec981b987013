package com.excelshare.dto;

import lombok.Data;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * Excel数据操作请求DTO
 * 
 * <AUTHOR> Team
 */
@Data
public class ExcelDataRequest {
    
    @NotNull(message = "Excel文件ID不能为空")
    private Long excelId;
    
    @NotNull(message = "行号不能为空")
    private Integer rowNumber;
    
    @NotNull(message = "单元格数据不能为空")
    private Map<String, Object> cellData;
}
