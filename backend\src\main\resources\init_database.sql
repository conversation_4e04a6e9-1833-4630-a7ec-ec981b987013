-- =====================================================
-- Excel共享平台数据库完整初始化脚本
-- 版本: 1.0.0
-- 创建时间: 2025-07-24
-- 说明: 包含完整的表结构和初始数据
-- =====================================================

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS excel_share_platform 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE excel_share_platform;

-- =====================================================
-- 表结构定义
-- =====================================================

-- 安全地重建数据库表结构
-- 禁用外键检查以避免删除顺序问题
SET FOREIGN_KEY_CHECKS = 0;

-- 删除所有可能存在的表（包括旧版本表）
DROP TABLE IF EXISTS column_permissions;
DROP TABLE IF EXISTS excel_data;
DROP TABLE IF EXISTS user_excel_data;  -- 旧版本表名
DROP TABLE IF EXISTS excel_files;
DROP TABLE IF EXISTS users;

-- 重新启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '加密密码',
    email VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱',
    role ENUM('ADMIN', 'USER') NOT NULL DEFAULT 'USER' COMMENT '角色',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除',
    
    -- 索引
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- Excel文件表
CREATE TABLE IF NOT EXISTS excel_files (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '文件ID',
    file_name VARCHAR(255) NOT NULL COMMENT '文件名',
    description TEXT COMMENT '文件描述',
    file_path VARCHAR(500) NOT NULL COMMENT '文件存储路径',
    created_by BIGINT NOT NULL COMMENT '创建者ID',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除',
    
    -- 索引
    INDEX idx_created_by (created_by),
    INDEX idx_file_name (file_name),
    INDEX idx_created_at (created_at),
    
    -- 外键约束
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Excel文件表';

-- Excel数据表
CREATE TABLE IF NOT EXISTS excel_data (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '数据ID',
    excel_id BIGINT NOT NULL COMMENT 'Excel文件ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    `row_number` INT NOT NULL COMMENT '行号',
    cell_data JSON NOT NULL COMMENT '单元格数据(JSON格式)',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引
    INDEX idx_excel_id (excel_id),
    INDEX idx_user_id (user_id),
    INDEX idx_row_number (`row_number`),
    INDEX idx_excel_row (excel_id, `row_number`),
    INDEX idx_excel_user (excel_id, user_id),
    
    -- 外键约束
    FOREIGN KEY (excel_id) REFERENCES excel_files(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Excel数据表';

-- 列权限表
CREATE TABLE IF NOT EXISTS column_permissions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '权限ID',
    excel_id BIGINT NOT NULL COMMENT 'Excel文件ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    column_name VARCHAR(10) NOT NULL COMMENT '列名(如A,B,C)',
    permission_type ENUM('read', 'WRITE') NOT NULL DEFAULT 'read' COMMENT '权限类型',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX idx_excel_id (excel_id),
    INDEX idx_user_id (user_id),
    INDEX idx_column_name (column_name),
    INDEX idx_excel_user_column (excel_id, user_id, column_name),
    
    -- 外键约束
    FOREIGN KEY (excel_id) REFERENCES excel_files(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- 唯一约束
    UNIQUE KEY uk_excel_user_column (excel_id, user_id, column_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='列权限表';

-- =====================================================
-- 初始数据插入
-- =====================================================

-- 插入默认用户（密码都是123456，已加密）
INSERT IGNORE INTO users (id, username, password, email, role, created_at, updated_at, deleted) VALUES
(1, 'admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', '<EMAIL>', 'ADMIN', NOW(), NOW(), FALSE),
(2, 'user', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', '<EMAIL>', 'USER', NOW(), NOW(), FALSE),
(3, 'test', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', '<EMAIL>', 'USER', NOW(), NOW(), FALSE);

-- 插入示例Excel文件
INSERT IGNORE INTO excel_files (id, file_name, description, file_path, created_by, created_at, updated_at, deleted) VALUES
(1, '员工信息表.xlsx', '公司员工基本信息管理表格', '/uploads/excel/employee_info.xlsx', 1, NOW(), NOW(), FALSE),
(2, '销售数据.xlsx', '2024年度销售数据统计表', '/uploads/excel/sales_data.xlsx', 1, NOW(), NOW(), FALSE),
(3, '项目进度.xlsx', '项目进度跟踪表', '/uploads/excel/project_progress.xlsx', 2, NOW(), NOW(), FALSE);

-- 插入示例Excel数据
INSERT IGNORE INTO excel_data (id, excel_id, user_id, `row_number`, cell_data, created_at, updated_at) VALUES
-- 员工信息表数据
(1, 1, 1, 1, '{"A":"姓名","B":"年龄","C":"部门","D":"职位","E":"入职日期"}', NOW(), NOW()),
(2, 1, 1, 2, '{"A":"张三","B":"25","C":"技术部","D":"软件工程师","E":"2023-01-15"}', NOW(), NOW()),
(3, 1, 1, 3, '{"A":"李四","B":"28","C":"销售部","D":"销售经理","E":"2022-06-20"}', NOW(), NOW()),
(4, 1, 1, 4, '{"A":"王五","B":"30","C":"市场部","D":"市场专员","E":"2023-03-10"}', NOW(), NOW()),
(5, 1, 2, 5, '{"A":"赵六","B":"26","C":"技术部","D":"前端工程师","E":"2023-08-01"}', NOW(), NOW()),

-- 销售数据表数据
(6, 2, 1, 1, '{"A":"月份","B":"销售额","C":"客户数","D":"成交率"}', NOW(), NOW()),
(7, 2, 1, 2, '{"A":"2024-01","B":"150000","C":"45","D":"75%"}', NOW(), NOW()),
(8, 2, 1, 3, '{"A":"2024-02","B":"180000","C":"52","D":"78%"}', NOW(), NOW()),
(9, 2, 1, 4, '{"A":"2024-03","B":"220000","C":"60","D":"80%"}', NOW(), NOW());

-- 插入列权限配置
INSERT IGNORE INTO column_permissions (id, excel_id, user_id, column_name, permission_type, created_at, updated_at) VALUES
-- 普通用户对员工信息表的权限
(1, 1, 2, 'A', 'read', NOW(), NOW()),      -- 姓名只读
(2, 1, 2, 'B', 'read', NOW(), NOW()),      -- 年龄只读
(3, 1, 2, 'C', 'WRITE', NOW(), NOW()),     -- 部门可写
(4, 1, 2, 'D', 'WRITE', NOW(), NOW()),     -- 职位可写
(5, 1, 2, 'E', 'read', NOW(), NOW()),      -- 入职日期只读

-- 测试用户对项目进度表的权限
(6, 3, 3, 'A', 'read', NOW(), NOW()),      -- 项目名称只读
(7, 3, 3, 'B', 'WRITE', NOW(), NOW()),     -- 进度可写
(8, 3, 3, 'C', 'WRITE', NOW(), NOW());     -- 状态可写

-- =====================================================
-- 数据库初始化完成
-- =====================================================

-- 显示创建的表
SHOW TABLES;

-- 显示用户数据
SELECT '=== 用户数据 ===' as info;
SELECT id, username, email, role, created_at FROM users;

-- 显示文件数据  
SELECT '=== 文件数据 ===' as info;
SELECT id, file_name, description, created_by, created_at FROM excel_files;

-- 显示权限数据
SELECT '=== 权限数据 ===' as info;
SELECT cp.excel_id, ef.file_name, u.username, cp.column_name, cp.permission_type 
FROM column_permissions cp
LEFT JOIN excel_files ef ON cp.excel_id = ef.id
LEFT JOIN users u ON cp.user_id = u.id
ORDER BY cp.excel_id, cp.user_id, cp.column_name;

SELECT '=== 数据库初始化完成 ===' as info;
